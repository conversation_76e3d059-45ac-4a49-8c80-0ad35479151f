import js  # To interact with JavaScript (DOM, console, alert, Blob, URL, localStorage)
from pyodide.ffi import create_proxy # To create JS callable proxies
import io  # To handle in-memory file-like objects
import csv # To handle CSV data generation
from datetime import datetime # To handle dates
from pyscript import document, display, when # PyScript helpers
import json # To serialize/deserialize Python objects for localStorage
import html # To escape potentially problematic characters in descriptions, etc.
from pyodide.http import pyfetch # To fetch JSON data
import asyncio # For async operations

# --- Constants ---
EXPENSES_STORAGE_KEY = "pyscript_expense_tracker_data"
USER_CONFIG_KEY = "pyscript_expense_tracker_config"
REGIONAL_PARTNERS_FILE = "support-partners.json"  # Keeping the same filename for backward compatibility

# --- Global State ---
expenses = [] # List to store expense dictionaries (will be loaded from localStorage)
current_edit_id = None # Track the ID of the expense being edited
userconfig = {
    'home-currency': "USD",
    'destination-currency': "USD",
    'exchange-rate': "1.00",
    'budgets': {
        'Food': "0.00",
        'Transport': "0.00",
        'Fuel': "0.00",
        'Entertainment': "0.00",
        'Utilities': "0.00",
        'Shopping': "0.00",
        'Groceries': "0.00",
        'Health': "0.00",
        'Other': "0.00",
        'Final': "0.00"
    }
} # Store currency names, exchange rate, and budget amounts
support_partners = {} # Dictionary to store regional partners data from JSON file
expense_categories = {} # Dictionary to store expense categories loaded from JSON file

# --- DOM Element References (get them once) ---
# Ensure these IDs match your index.html exactly!
description_main_input = document.getElementById("desc_main")
description_additional_input = document.getElementById("desc_additional")
amount_home_input = document.getElementById("amount_home")
amount_destination_input = document.getElementById("amount_destination")
date_input = document.getElementById("date")
category_input = document.getElementById("category")
payment_method_input = document.getElementById("payment_method")
location_input = document.getElementById("location")
tags_input = document.getElementById("tags")
expense_goal_input = document.getElementById("expense_goal")
expense_list_tbody = document.getElementById("expense-list") # For the table body
expense_count_element = document.getElementById("expense-count") # For displaying the count of expenses
status_area = document.getElementById("status_area")

# <<< NEW: Get reference to the card container >>>
expense_cards_container = document.getElementById("expense-cards-container")

# --- Functions ---

# Function calculate_total_expenses removed as per requirements

def save_expenses():
    """Saves the current expenses list to localStorage."""
    try:
        # Prepare data for saving (ensure consistency)
        expenses_to_save = []
        for i, exp in enumerate(expenses):
            expenses_to_save.append({
                'exp_id': exp.get('id', i + 1), # Assign ID if missing, ensure uniqueness might be needed later
                'description_main': exp.get('desc_main', ''),
                'description_additional': exp.get('desc_additional', ''),
                'description': exp.get('description', ''), # Recalculated on load/add if needed
                'amount_home': exp.get('amount_home', exp.get('amount', 0.0)),  # Backward compatibility
                'amount_destination': exp.get('amount_destination', 0.0),
                'currency_home': exp.get('currency_home', userconfig['home-currency']),  # Store the currency
                'currency_destination': exp.get('currency_destination', userconfig['destination-currency']),  # Store the currency
                'expense_roe': exp.get('expense_roe', userconfig['exchange-rate']),  # Store the exchange rate
                'date': exp.get('date', ''),
                'category': exp.get('category', 'Other'),
                'category_budget': exp.get('category_budget', userconfig['budgets'].get(exp.get('category', 'Other'), "0.00")),  # Store the budget amount
                'payment_method': exp.get('payment_method', ''),
                'location': exp.get('location', ''),
                'tags': exp.get('tags', []),
                'expense_goal': exp.get('expense_goal', ''),
                'support_partner': exp.get('support_partner', '')
            })

        expenses_json = json.dumps(expenses_to_save)
        js.localStorage.setItem(EXPENSES_STORAGE_KEY, expenses_json)
        print(f"Saved {len(expenses)} expenses to localStorage.")
    except Exception as e:
        print(f"Error saving expenses to localStorage: {e}")
        show_status("Error saving data. Changes might be lost on refresh.", is_success=False)

def load_expenses():
    """Loads expenses from localStorage on startup."""
    global expenses # We need to modify the global list
    try:
        stored_data = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
        if stored_data:
            print("Found saved data in localStorage. Loading...") # Console log
            loaded_list = json.loads(stored_data)
            temp_expenses = []
            for i, item in enumerate(loaded_list):
                try:
                    date_obj = datetime.strptime(item['date'], '%Y-%m-%d').date()
                    # Reconstruct the description if necessary or use stored one
                    # Handle both new and old variable names for backward compatibility
                    desc_main = item.get('description_main', item.get('description_primary', ''))
                    desc_additional = item.get('description_additional', item.get('description_secondary', ''))
                    description = f"{desc_main} - {desc_additional}" if desc_additional else desc_main

                    # Get the category and its budget
                    category = item.get('category', 'Other')
                    category_budget = item.get('category_budget', userconfig['budgets'].get(category, "0.00"))

                    temp_expenses.append({
                        'id': int(item.get('exp_id', i + 1)), # Use saved ID or generate
                        'desc_main': desc_main,
                        'desc_additional': desc_additional,
                        'description': description,
                        'amount_home': float(item.get('amount_home', item.get('amount', 0.0))), # Backward compatibility
                        'amount_destination': float(item.get('amount_destination', 0.0)),
                        'currency_home': item.get('currency_home', userconfig['home-currency']),  # Load stored currency or use default
                        'currency_destination': item.get('currency_destination', userconfig['destination-currency']),  # Load stored currency or use default
                        'expense_roe': item.get('expense_roe', userconfig['exchange-rate']),  # Load stored exchange rate or use default
                        'date': item['date'],
                        'date_obj': date_obj, # Store the date object for sorting
                        'category': category,
                        'category_budget': category_budget,  # Load stored budget or use default
                        'payment_method': item.get('payment_method', ''),
                        'location': item.get('location', ''),
                        'tags': item.get('tags', []), # Assume tags are stored as a list
                        'expense_goal': item.get('expense_goal', ''),
                        'support_partner': item.get('support_partner', '')
                    })
                except (ValueError, KeyError, TypeError) as parse_err:
                     print(f"Skipping corrupted item during load: {item}, Error: {parse_err}")
            expenses = temp_expenses # Assign loaded data to global variable
            print(f"Loaded {len(expenses)} expenses.")
        else:
            print("No saved data found in localStorage.")
            expenses = [] # Ensure it's an empty list if nothing is loaded
    except Exception as e:
        print(f"Error loading expenses from localStorage: {e}")
        expenses = []
        show_status("Could not load saved data. Starting fresh.", is_success=False)


def get_category_group(category):
    """Extracts the category group from a full category string (e.g., 'Food-Groceries' -> 'Food')."""
    if not category:
        return 'Other'

    # Split by dash and take the first part as the group
    parts = category.split('-')
    return parts[0] if parts else 'Other'

def get_icon_html(category):
    """Returns HTML for an icon based on the expense category group."""
    category_group = get_category_group(category).lower()
    icon_path_prefix = "./icons/"

    # Map category groups to their corresponding icon files
    icon_mapping = {
        'accommodation': 'accommodation.png',
        'education': 'education.png',
        'entertainment': 'entertainment.png',
        'financial': 'financial.png',
        'food': 'food.png',
        'gifts': 'gifts.png',
        'health': 'health.png',
        'home': 'home.png',
        'kids': 'kids.png',
        'personalcare': 'personalcare.png',
        'pets': 'pets.png',
        'shopping': 'shopping.png',
        'transport': 'transport.png',
        'travel': 'travel.png',
        'utilities': 'utilities.png',
        'work': 'work.png',
        'other': 'other.png'
    }

    # Get the icon file name, default to 'other.png' if not found
    icon_file = icon_mapping.get(category_group, 'other.png')

    return f'<img src="{icon_path_prefix}{icon_file}" alt="{category_group.title()}" title="{category}" style="width:40px; height:40px;">'

def populate_category_dropdown():
    """Populates the category dropdown with options from the loaded expense categories."""
    category_select = document.getElementById("category")
    if not category_select or not expense_categories:
        return

    # Clear existing options except the first one (placeholder)
    while category_select.children.length > 1:
        category_select.removeChild(category_select.lastChild)

    # Create a flat list of all categories
    all_categories = []
    for group, subcategories in expense_categories.items():
        for subcategory in subcategories:
            category_value = f"{group}-{subcategory}"
            category_display = category_value
            all_categories.append((category_value, category_display))

    # Sort categories alphabetically by display name
    all_categories.sort(key=lambda x: x[1])

    # Add options to the select element
    for category_value, category_display in all_categories:
        option = document.createElement("option")
        option.value = category_value
        option.textContent = category_display
        category_select.appendChild(option)


# <<< MODIFIED: render_expenses function >>>
def render_expenses():
    """Updates BOTH the expense cards and the HTML table with the current expenses."""
    # Check if containers exist
    if not expense_list_tbody:
        print("Error: Cannot find expense list table body (ID: expense-list).")
        return
    if not expense_cards_container:
        print("Error: Cannot find expense cards container (ID: expense-cards-container).")
        return

    # Clear previous content from both containers
    expense_list_tbody.innerHTML = ""
    expense_cards_container.innerHTML = "" # Clear cards

    # Handle case where there are no expenses
    if not expenses:
        # Add message to table
        row = document.createElement("tr")
        cell = document.createElement("td")
        cell.colSpan = 14 # Number of columns in your table
        cell.textContent = "No expenses added yet."
        cell.style.textAlign = "center"
        row.appendChild(cell)
        expense_list_tbody.appendChild(row)

        # Add message to card container
        no_cards_msg = document.createElement("p")
        no_cards_msg.textContent = "No expenses recorded yet."
        no_cards_msg.style.textAlign = "center"
        no_cards_msg.style.color = "#666"
        expense_cards_container.appendChild(no_cards_msg)
        return # Exit early

    # Sort expenses by ID only (highest ID first)
    # This ensures that the most recently added expenses are always at the top
    sorted_expenses = sorted(expenses, key=lambda x: -x.get('id', 0))

    # For the main page, limit to 3 preview cards for the card view
    preview_expenses = sorted_expenses[:3]

    # First, create the table rows from the full sorted list
    for expense in sorted_expenses:
        # Create Table Row
        row = document.createElement("tr")
        tags_str = ', '.join(expense.get('tags', [])) if isinstance(expense.get('tags', []), list) else expense.get('tags', '-')

        # Define cell data (use html.escape for potentially user-entered text)
        cells_data = [
            expense['date'],
            html.escape(expense['desc_main']),
            html.escape(expense.get('desc_additional', '')),
            f"{expense.get('amount_home', 0.0):.2f}",
            expense.get('currency_home', userconfig['home-currency']),  # Use stored currency or default
            f"{expense.get('amount_destination', 0.0):.2f}",
            expense.get('currency_destination', userconfig['destination-currency']),  # Use stored currency or default
            expense.get('expense_roe', userconfig['exchange-rate']),  # Use stored exchange rate or default
            html.escape(expense['category']),
            expense.get('category_budget', "0.00"),  # Use the stored category_budget value
            html.escape(expense['payment_method']),
            html.escape(expense.get('location', '-')),
            html.escape(tags_str),
            html.escape(expense.get('expense_goal', '-'))
        ]

        for i, value in enumerate(cells_data):
            cell = document.createElement("td")
            cell.textContent = value
            # Apply specific styles from previous logic
            if i == 3 or i == 5: # Amount columns
                cell.style.textAlign = "right"
            if i == 4 or i == 6 or i == 7: # Currency and category columns
                cell.style.textAlign = "center"
            # Add class for potential hover effect on long text
            if i == 1 or i == 2 or i == 11 or i == 12: # Description/Location/Tags/Goal columns
                 cell.classList.add("expandable-cell")
                 cell.setAttribute("data-full-text", value) # Store full text for CSS tooltip
            row.appendChild(cell)

        # Add delete button to table row
        action_cell = document.createElement("td")
        delete_button = document.createElement("button")
        delete_button.textContent = "Delete"
        delete_button.classList.add("delete-button") # Add a class for potential CSS styling

        # IMPORTANT: Pass the unique expense ID to the delete function if available
        expense_id = expense.get('id')
        if expense_id is not None:
            delete_proxy = create_proxy(lambda _event, item_id=expense_id: delete_expense_item(item_id))
        else:
             # Fallback if ID is missing (less reliable)
             delete_proxy = create_proxy(lambda _event, item_data=expense: delete_expense_item_by_data(item_data))
        delete_button.addEventListener("click", delete_proxy)

        action_cell.appendChild(delete_button)
        row.appendChild(action_cell)
        expense_list_tbody.appendChild(row) # Append row to table body

    # Now, create the card HTML for the preview cards only
    all_cards_html = "" # Accumulate card HTML strings

    # Use preview_expenses (limited to 6) for the cards on the main page
    for expense in preview_expenses:
        # Get tags string for the card
        tags_str = ', '.join(expense.get('tags', [])) if isinstance(expense.get('tags', []), list) else expense.get('tags', '-')

        # --- 2. Create Card HTML ---
        icon_html = get_icon_html(expense['category']) # Get the icon
        card_html = f"""
        <div class="expense-card" data-expense-id="{expense.get('id', '')}">
            <div class="card-icon">
                {icon_html}
            </div>
            <div class="card-details">
                <p><strong>{html.escape(expense['desc_main'])}</strong></p>
                <p>{html.escape(expense.get('desc_additional', ''))}</p>
                <p><strong>Date:</strong> {html.escape(expense['date'])}</p>
                <p>
                    <strong>{expense.get('currency_home', userconfig['home-currency'])}:</strong>
                    <span style="font-weight: bold;">{expense.get('amount_home', 0.0):.2f}</span> |
                    <strong>{expense.get('currency_destination', userconfig['destination-currency'])}:</strong>
                    <span style="font-weight: bold;">{expense.get('amount_destination', 0.0):.2f}</span>
                </p>
                <p>
                    <strong>Category:</strong> {html.escape(expense['category'])} |
                    <strong>Method:</strong> {html.escape(expense['payment_method'])}
                </p>
            </div>
        </div>
        """
        all_cards_html += card_html # Append to the accumulated string

    # --- 3. Update DOM after loop ---
    expense_cards_container.innerHTML = all_cards_html # Set the card container content

    # No need to add event listeners for edit/delete buttons on the main page
    # since we've removed those buttons from the cards


    # Update the expense count
    if expense_count_element:
        expense_count_element.innerText = str(len(expenses))


def show_status(message, is_success=True):
    """Displays a status message to the user."""
    if not status_area:
         print(f"Status Error: Cannot find status area element. Message: {message}")
         js.alert(f"Status: {message}") # Fallback to alert if status area missing
         return

    status_area.innerHTML = ""
    div = document.createElement("div")
    div.textContent = message
    div.classList.add("status-message")
    div.classList.add("success" if is_success else "error")
    status_area.appendChild(div)
    # Clear message after 3 seconds using setTimeout from js module
    clear_proxy = create_proxy(lambda: status_area.removeChild(div) if status_area.contains(div) else None)
    js.setTimeout(clear_proxy, 3000)


# Event handler needs to accept the event object
@when("submit", "#expense-form")
def add_expense_handler(event):
    """Handles the form submission to add a new expense or update an existing one."""
    event.preventDefault() # Prevent default form submission
    global current_edit_id

    required_inputs = [
        description_main_input, amount_home_input, date_input,
        category_input, payment_method_input
    ]
    if not all(required_inputs):
        show_status("Error: Required input fields not found in the form.", is_success=False)
        return

    desc_main = description_main_input.value.strip()
    desc_additional = description_additional_input.value.strip() if description_additional_input else ""
    amount_home_str = amount_home_input.value.strip()
    amount_destination_str = amount_destination_input.value.strip() if amount_destination_input else "0"
    date_str = date_input.value
    category = category_input.value
    payment_method = payment_method_input.value
    location = location_input.value.strip() if location_input else ""
    tags_raw = tags_input.value if tags_input else ""
    tags = [tag.strip() for tag in tags_raw.split(",") if tag.strip()] # Filter empty tags
    expense_goal = expense_goal_input.value.strip() if expense_goal_input else ""

    # Get the regional partner based on the destination currency
    # If destination currency is "None", use the home currency instead
    if userconfig['destination-currency'] == "None":
        support_partner = get_support_partner(userconfig['home-currency'])
    else:
        support_partner = get_support_partner(userconfig['destination-currency'])

    # Validation
    if not all([desc_main, date_str, category, payment_method]):
        show_status("Please fill in all required fields (*).", is_success=False) # Added (*) hint
        return

    # Initialize amount variables
    amount_home_float = 0.0
    amount_destination_float = 0.0

    # Process Home Currency Amount if provided
    if amount_home_str:
        try:
            amount_home_float = float(amount_home_str)
            if amount_home_float < 0:
                show_status("Home Currency Amount cannot be negative.", is_success=False)
                return
        except ValueError:
            show_status("Invalid Home Currency Amount entered. Please use numbers.", is_success=False)
            return

    # Process Destination Currency Amount if provided
    if amount_destination_str:
        try:
            amount_destination_float = float(amount_destination_str)
            if amount_destination_float < 0:
                show_status("Destination Currency Amount cannot be negative.", is_success=False)
                return
        except ValueError:
            show_status("Invalid Destination Currency Amount entered. Please use numbers.", is_success=False)
            return

    # Ensure at least one amount is provided
    if amount_home_float == 0.0 and amount_destination_float == 0.0:
        show_status("Please enter either Home Currency Amount or Destination Currency Amount.", is_success=False)
        return

    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        show_status("Invalid date format. Please use YYYY-MM-DD.", is_success=False)
        return

    # Get the budget amount for the selected category
    category_budget = userconfig['budgets'].get(category, "0.00")

    # Prepare the expense data
    expense_data = {
        'desc_main': desc_main,
        'desc_additional': desc_additional,
        'description': f"{desc_main} - {desc_additional}" if desc_additional else desc_main,
        'amount_home': amount_home_float,
        'amount_destination': amount_destination_float,
        'currency_home': userconfig['home-currency'],  # Store the current home currency
        'currency_destination': userconfig['destination-currency'],  # Store the current destination currency
        'expense_roe': userconfig['exchange-rate'],  # Store the current exchange rate
        'date': date_str,
        'date_obj': date_obj,
        'category': category,
        'category_budget': category_budget,  # Store the budget amount for this category
        'payment_method': payment_method,
        'location': location,
        'tags': tags,
        'expense_goal': expense_goal,
        'support_partner': support_partner
    }

    # If we're editing an existing expense, preserve the original category_budget
    # unless the category has changed
    if current_edit_id is not None:
        for exp in expenses:
            if exp.get('id') == current_edit_id:
                if exp.get('category') == category:
                    # Category hasn't changed, keep the original budget
                    expense_data['category_budget'] = exp.get('category_budget', category_budget)
                break

    # Check if we're editing an existing expense or adding a new one
    if current_edit_id is not None:
        # Update existing expense
        for i, exp in enumerate(expenses):
            if exp.get('id') == current_edit_id:
                # Preserve the ID
                expense_data['id'] = current_edit_id
                # Replace the expense in the list
                expenses[i] = expense_data
                save_expenses()
                show_status("Expense updated successfully!", is_success=True)
                break
        else:
            # This executes if the loop completes without finding the expense
            show_status(f"Error: Could not find expense with ID {current_edit_id} to update.", is_success=False)
    else:
        # Add new expense
        # Generate a more reliable unique ID
        new_id = max([exp.get('id', 0) for exp in expenses] + [0]) + 1
        expense_data['id'] = new_id
        expenses.append(expense_data)
        save_expenses()
        show_status("Expense added successfully!", is_success=True)

    # Reset the form and edit state
    description_main_input.value = ""
    if description_additional_input: description_additional_input.value = ""
    amount_home_input.value = ""
    if amount_destination_input: amount_destination_input.value = ""
    if category_input: category_input.value = "" # Reset category dropdown
    if payment_method_input: payment_method_input.value = "" # Reset payment dropdown
    if location_input: location_input.value = ""
    if tags_input: tags_input.value = ""
    if expense_goal_input: expense_goal_input.value = ""

    # Reset the edit state
    current_edit_id = None

    # Reset the submit button text
    submit_button = document.querySelector('#expense-form button[type="submit"]')
    if submit_button:
        submit_button.textContent = "Add Expense"

    description_main_input.focus() # Focus on first field
    render_expenses() # Update both views


# <<< MODIFIED: Delete function using item ID >>>
def delete_expense_item(item_id):
    """Deletes an expense item by its unique ID."""
    global expenses
    original_length = len(expenses)
    # Filter out the expense with the matching ID
    expenses = [exp for exp in expenses if exp.get('id') != item_id]

    if len(expenses) < original_length:
        save_expenses()
        show_status("Expense deleted.", is_success=True)
    else:
        print(f"Error: Could not find expense with ID {item_id} to delete.")
        show_status("Error: Could not find the expense to delete.", is_success=False)

    render_expenses() # Re-render the list regardless

# Fallback delete function (less reliable, used if ID is missing)
def delete_expense_item_by_data(item_to_delete):
     """Deletes an expense item by matching its data (fallback)."""
     global expenses
     original_length = len(expenses)
     # Try to find a match based on several fields
     expenses = [
         exp for exp in expenses
         if not (exp.get('description') == item_to_delete.get('description') and
                 exp.get('amount') == item_to_delete.get('amount') and
                 exp.get('date') == item_to_delete.get('date'))
     ]
     if len(expenses) < original_length:
        save_expenses()
        show_status("Expense deleted (fallback match).", is_success=True)
     else:
        print("Debug: Item to delete (by data) not found in expenses list:", item_to_delete)
        show_status("Error: Could not find the specific expense to delete (fallback).", is_success=False)
     render_expenses()


def edit_expense_item(item_id):
    """Populates the form with expense data for editing."""
    global current_edit_id

    # Find the expense with the matching ID
    expense_to_edit = None
    for exp in expenses:
        if exp.get('id') == item_id:
            expense_to_edit = exp
            break

    if not expense_to_edit:
        show_status(f"Error: Could not find expense with ID {item_id} to edit.", is_success=False)
        return

    # Set the current edit ID
    current_edit_id = item_id

    # Populate the form with the expense data
    description_main_input.value = expense_to_edit.get('desc_main', '')
    if description_additional_input:
        description_additional_input.value = expense_to_edit.get('desc_additional', '')
    amount_home_input.value = str(expense_to_edit.get('amount_home', expense_to_edit.get('amount', '')))
    if amount_destination_input:
        amount_destination_input.value = str(expense_to_edit.get('amount_destination', ''))
    date_input.value = expense_to_edit.get('date', '')
    if category_input:
        category_input.value = expense_to_edit.get('category', '')
    if payment_method_input:
        payment_method_input.value = expense_to_edit.get('payment_method', '')
    if location_input:
        location_input.value = expense_to_edit.get('location', '')
    if tags_input:
        tags_input.value = ', '.join(expense_to_edit.get('tags', []))
    if expense_goal_input:
        expense_goal_input.value = expense_to_edit.get('expense_goal', '')

    # Scroll to the form and focus on the first field
    description_main_input.scrollIntoView()
    description_main_input.focus()

    # Change the submit button text to indicate editing mode
    submit_button = document.querySelector('#expense-form button[type="submit"]')
    if submit_button:
        submit_button.textContent = "Update Expense"

    show_status(f"Editing expense from {expense_to_edit.get('date', '')}. Update the form and submit to save changes.", is_success=True)


def import_csv_data(csv_text):
    """Imports expense data from a CSV string.

    Args:
        csv_text: CSV data as a string

    Returns:
        list: Imported expense data
        str: Status message
    """
    try:
        # Use StringIO to treat the string as a file
        csv_buffer = io.StringIO(csv_text)

        # Skip BOM if present
        if csv_text.startswith('\ufeff'):
            csv_buffer = io.StringIO(csv_text[1:])

        # Create a CSV reader
        reader = csv.reader(csv_buffer)

        # Read the header row
        header = next(reader)
        print(f"CSV Header: {header}")

        # Print the raw CSV data for debugging
        print(f"Raw CSV data (first 200 chars): {csv_text[:200]}")

        # Map header indices
        header_map = {}
        expected_headers = [
            'ID', 'Date', 'Primary Description', 'Additional Description',
            'Home Currency Amount', 'Home Currency',
            'Destination Currency Amount', 'Destination Currency',
            'Exchange Rate',
            'Category', 'Category Budget', 'Payment Method', 'Location', 'Tags',
            'Expense Goal', 'Regional Partner', 'Support Partner'  # Include both for backward compatibility
        ]

        # Also check for old header format and handle variations
        old_headers = {
            'Amount': 'Home Currency Amount',
            'Primary Currency': 'Home Currency',  # Map to new column name
            'Secondary Currency': 'Destination Currency',  # Map to new column name
            'Secondary Description': 'Additional Description',  # Map old header to new name
            'Support Partner': 'Regional Partner',  # Map old header to new name
            'ID': None,  # Ignore this column
            'Home Currency': 'Home Currency',  # Ensure exact match
            'Destination Currency': 'Destination Currency'  # Ensure exact match
        }

        # Map header indices
        for i, col in enumerate(header):
            # Check for direct match
            if col in expected_headers:
                header_map[col] = i
                print(f"Found header: '{col}' at index {i}")
            # Check for old header format
            elif col in old_headers and old_headers[col] is not None:
                header_map[old_headers[col]] = i
                print(f"Mapped old header: '{col}' to '{old_headers[col]}' at index {i}")
            else:
                print(f"Ignoring unknown header: '{col}' at index {i}")

        # Print the final header map
        print("Final header map:")
        for header_name, index in header_map.items():
            print(f"  {header_name}: {index}")

        # Validate that we have the minimum required headers
        required_headers = ['Date', 'Primary Description']
        missing_headers = [h for h in required_headers if h not in header_map]
        if missing_headers:
            print(f"Missing headers: {missing_headers}")
            print(f"Found headers: {header}")
            return [], f"Missing required headers: {', '.join(missing_headers)}"

        # Parse the data rows
        imported_expenses = []
        for row_num, row in enumerate(reader, start=2):  # Start at 2 to account for header row
            try:
                # Skip empty rows
                if not row or all(cell.strip() == '' for cell in row):
                    continue

                # Extract data using the header map
                expense_data = {}

                # Get date
                date_str = row[header_map['Date']].strip()
                try:
                    # Try different date formats
                    try:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                    except ValueError:
                        # Try alternative format with +AC0- encoding
                        date_str = date_str.replace('+AC0-', '-')
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                except ValueError:
                    print(f"Warning: Invalid date format in row {row_num}: {date_str}")
                    continue

                # Get descriptions
                desc_main = row[header_map['Primary Description']].strip()
                # Replace encoded characters
                desc_main = desc_main.replace('+AC0-', '-')

                desc_additional = ''
                if 'Additional Description' in header_map and header_map.get('Additional Description', -1) < len(row):
                    desc_additional = row[header_map.get('Additional Description', -1)].strip()
                    # Replace encoded characters
                    desc_additional = desc_additional.replace('+AC0-', '-')

                # Get amounts
                amount_home = 0.0
                if 'Home Currency Amount' in header_map and header_map['Home Currency Amount'] < len(row):
                    amount_home_str = row[header_map['Home Currency Amount']].strip()
                    try:
                        amount_home = float(amount_home_str) if amount_home_str else 0.0
                    except ValueError:
                        print(f"Warning: Invalid home amount format in row {row_num}: '{amount_home_str}'")

                amount_destination = 0.0
                if 'Destination Currency Amount' in header_map and header_map['Destination Currency Amount'] < len(row):
                    amount_destination_str = row[header_map['Destination Currency Amount']].strip()
                    try:
                        amount_destination = float(amount_destination_str) if amount_destination_str else 0.0
                    except ValueError:
                        print(f"Warning: Invalid destination amount format in row {row_num}: '{amount_destination_str}'")

                # Print row data for debugging
                print(f"Row {row_num}: Date={date_str}, Desc={desc_main}, Home={amount_home}, Dest={amount_destination}")

                # Get currency values if available
                currency_home = userconfig['home-currency']
                if 'Home Currency' in header_map and header_map['Home Currency'] < len(row):
                    currency_home_str = row[header_map['Home Currency']].strip()
                    print(f"  Home Currency in CSV: '{currency_home_str}'")
                    if currency_home_str:
                        currency_home = currency_home_str
                        print(f"  Using Home Currency from CSV: {currency_home}")
                    else:
                        print(f"  Using default Home Currency: {currency_home}")
                else:
                    print(f"  'Home Currency' column not found in CSV, using default: {currency_home}")

                currency_destination = userconfig['destination-currency']
                if 'Destination Currency' in header_map and header_map['Destination Currency'] < len(row):
                    currency_destination_str = row[header_map['Destination Currency']].strip()
                    print(f"  Destination Currency in CSV: '{currency_destination_str}'")
                    if currency_destination_str:
                        currency_destination = currency_destination_str
                        print(f"  Using Destination Currency from CSV: {currency_destination}")
                    else:
                        print(f"  Using default Destination Currency: {currency_destination}")
                else:
                    print(f"  'Destination Currency' column not found in CSV, using default: {currency_destination}")

                # Ensure at least one amount is provided
                if amount_home == 0.0 and amount_destination == 0.0:
                    print(f"Warning: No valid amount provided in row {row_num}")
                    continue

                # Get other fields
                category = row[header_map.get('Category', -1)].strip() if 'Category' in header_map else 'Other'

                # Get category budget if available
                # First, check if it's in the CSV file
                category_budget = "0.00"
                if 'Category Budget' in header_map and header_map['Category Budget'] < len(row):
                    category_budget_str = row[header_map['Category Budget']].strip()
                    if category_budget_str:
                        category_budget = category_budget_str
                        print(f"  Using Category Budget from CSV: {category_budget}")
                    else:
                        # If not in CSV, use the budget from userconfig
                        category_budget = userconfig['budgets'].get(category, "0.00")
                        print(f"  Using Category Budget from userconfig: {category_budget}")
                else:
                    # If Category Budget column doesn't exist, use the budget from userconfig
                    category_budget = userconfig['budgets'].get(category, "0.00")
                    print(f"  'Category Budget' column not found in CSV, using from userconfig: {category_budget}")

                payment_method = row[header_map.get('Payment Method', -1)].strip() if 'Payment Method' in header_map else ''
                location = row[header_map.get('Location', -1)].strip() if 'Location' in header_map else ''
                tags_str = row[header_map.get('Tags', -1)].strip() if 'Tags' in header_map else ''
                tags = [tag.strip() for tag in tags_str.split(',')] if tags_str else []



                # Get exchange rate if available
                exchange_rate = userconfig['exchange-rate']
                if 'Exchange Rate' in header_map and header_map['Exchange Rate'] < len(row):
                    exchange_rate_str = row[header_map['Exchange Rate']].strip()
                    if exchange_rate_str:
                        exchange_rate = exchange_rate_str

                # Get expense goal and support partner if available
                expense_goal = ''
                if 'Expense Goal' in header_map and header_map['Expense Goal'] < len(row):
                    expense_goal = row[header_map['Expense Goal']].strip()

                # Get support partner based on the destination currency
                # If destination currency is "None", use the home currency instead
                if currency_destination == "None":
                    support_partner = get_support_partner(currency_home)
                else:
                    support_partner = get_support_partner(currency_destination)

                # Override with the value from CSV if provided
                if 'Regional Partner' in header_map and header_map['Regional Partner'] < len(row) and row[header_map['Regional Partner']].strip():
                    support_partner = row[header_map['Regional Partner']].strip()
                # For backward compatibility, also check for Support Partner
                elif 'Support Partner' in header_map and header_map['Support Partner'] < len(row) and row[header_map['Support Partner']].strip():
                    support_partner = row[header_map['Support Partner']].strip()

                # Create the expense data dictionary
                expense_data = {
                    'desc_main': desc_main,
                    'desc_additional': desc_additional,
                    'description': f"{desc_main} - {desc_additional}" if desc_additional else desc_main,
                    'amount_home': amount_home,
                    'amount_destination': amount_destination,
                    'currency_home': currency_home,
                    'currency_destination': currency_destination,
                    'expense_roe': exchange_rate,
                    'date': date_str,
                    'date_obj': date_obj,
                    'category': category,
                    'category_budget': category_budget,
                    'payment_method': payment_method,
                    'location': location,
                    'tags': tags,
                    'expense_goal': expense_goal,
                    'support_partner': support_partner
                }

                # Add ID if available
                if 'ID' in header_map and header_map['ID'] < len(row) and row[header_map['ID']].strip():
                    try:
                        expense_data['id'] = int(row[header_map['ID']])
                        print(f"  Using ID from CSV: {expense_data['id']}")
                    except ValueError:
                        print(f"  Invalid ID in CSV: '{row[header_map['ID']]}'")
                        pass  # Ignore invalid IDs

                imported_expenses.append(expense_data)

            except Exception as row_error:
                print(f"Error processing row {row_num}: {row_error}")
                continue

        return imported_expenses, f"Successfully imported {len(imported_expenses)} expenses."

    except Exception as e:
        print(f"Error importing CSV: {e}")
        return [], f"Error importing CSV: {e}"


@when("click", "#export-button")
def export_data_handler(_event): # Using underscore to indicate unused parameter
    """Exports the current expenses list to a CSV file and triggers download."""
    if not expenses:
        show_status("No expenses to export.", is_success=False)
        return

    # Use a buffer that handles Unicode correctly
    output = io.StringIO()
    # Define fieldnames based on the simplified CSV format
    fieldnames = [
        'ID', 'Date', 'Primary Description', 'Additional Description',
        'Home Currency Amount', 'Home Currency',
        'Destination Currency Amount', 'Destination Currency',
        'Exchange Rate',
        'Category', 'Category Budget', 'Payment Method', 'Location', 'Tags',
        'Expense Goal', 'Regional Partner'
    ]
    # Use standard csv.writer with explicit line endings and dialect
    writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL, lineterminator='\r\n')
    writer.writerow(fieldnames) # Write header row

    # Sort by date before exporting (oldest first for CSV export)
    # Keep using date for CSV export as it makes more sense for external tools
    sorted_expenses_export = sorted(expenses, key=lambda x: (x.get('date_obj', datetime.min.date()), x.get('id', 0)), reverse=False)

    for expense in sorted_expenses_export:
        tags_str = ','.join(expense.get('tags', [])) if isinstance(expense.get('tags'), list) else expense.get('tags', '')
        # Format amounts as strings with 2 decimal places
        amount_home = f"{expense.get('amount_home', expense.get('amount', 0.0)):.2f}"
        amount_destination = f"{expense.get('amount_destination', 0.0):.2f}"

        # Replace 0.00 with empty string for better readability
        if amount_home == "0.00": amount_home = ""
        if amount_destination == "0.00": amount_destination = ""

        row_data = [
            expense.get('id', ''),  # Include the expense ID
            expense.get('date', ''),
            expense.get('desc_main', ''),
            expense.get('desc_additional', ''),
            amount_home,
            expense.get('currency_home', userconfig['home-currency']),
            amount_destination,
            expense.get('currency_destination', userconfig['destination-currency']),
            expense.get('expense_roe', userconfig['exchange-rate']),
            expense.get('category', ''),
            expense.get('category_budget', "0.00"),
            expense.get('payment_method', ''),
            expense.get('location', ''),
            tags_str,
            expense.get('expense_goal', ''),
            expense.get('support_partner', '')
        ]
        writer.writerow(row_data)

    csv_data = output.getvalue()
    output.close()

    # Debug: Print a sample of the CSV data
    print(f"CSV Sample (first 200 chars): {csv_data[:200]}")

    try:
        # Ensure correct blob type for CSV with BOM for Excel compatibility
        # Add UTF-8 BOM (Byte Order Mark) to help Excel recognize UTF-8 encoding
        utf8_bom = '\ufeff'
        csv_data_with_bom = utf8_bom + csv_data
        # Use text/csv MIME type with UTF-8 encoding
        blob = js.Blob.new([csv_data_with_bom], {type: "text/csv;charset=utf-8;"})
        url = js.URL.createObjectURL(blob)
        link = document.createElement("a")
        link.href = url
        # Use a timestamp in the filename
        filename = f"expenses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        link.download = filename
        document.body.appendChild(link) # Append link to body to ensure click works in all browsers
        link.click() # Simulate click to trigger download
        document.body.removeChild(link) # Clean up the link
        js.URL.revokeObjectURL(url) # Release the object URL
        show_status(f"Data exported to {filename}", is_success=True)
    except Exception as e:
        print(f"Error during CSV export: {e}")
        show_status(f"Could not export CSV: {e}", is_success=False)


# --- Initial Setup ---
async def load_support_partners():
    """Loads the regional partners data from the JSON file."""
    global support_partners
    try:
        response = await pyfetch(REGIONAL_PARTNERS_FILE)
        if response.status == 200:
            support_partners = await response.json()
            print("Loaded regional partners:", support_partners)
            return True
        else:
            print(f"Error loading regional partners: HTTP {response.status}")
            return False
    except Exception as e:
        print(f"Error loading regional partners: {e}")
        return False

async def load_expense_categories():
    """Loads the expense categories data from the JSON file."""
    global expense_categories
    try:
        response = await pyfetch("expense_category_list.json")
        if response.status == 200:
            expense_categories = await response.json()
            print("Loaded expense categories:", list(expense_categories.keys()))
            return True
        else:
            print(f"Error loading expense categories: HTTP {response.status}")
            return False
    except Exception as e:
        print(f"Error loading expense categories: {e}")
        return False

def get_support_partner(currency):
    """Gets the regional partner for a given currency.

    If the currency is "None", use the Home Currency instead.
    """
    # If currency is None or "None", use the Home Currency instead
    if not currency or currency == "None":
        home_currency = userconfig['home-currency']
        if home_currency in support_partners:
            return support_partners[home_currency]
        return f"Unknown Regional Partner for {home_currency}"

    # Otherwise, use the provided currency
    if currency in support_partners:
        return support_partners[currency]
    return f"Unknown Regional Partner for {currency}"

def load_user_config():
    """Loads user configuration from localStorage."""
    global userconfig
    try:
        stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
        if stored_config:
            config = json.loads(stored_config)
            print("Loaded user configuration:", config)

            # Update the global userconfig
            if 'home-currency' in config:
                userconfig['home-currency'] = config['home-currency']

            if 'destination-currency' in config:
                userconfig['destination-currency'] = config['destination-currency']

            if 'exchange-rate' in config:
                userconfig['exchange-rate'] = config['exchange-rate']
            else:
                userconfig['exchange-rate'] = "1.00"  # Default value

            # Load budget amounts if available
            if 'budgets' in config:
                userconfig['budgets'] = config['budgets']
            else:
                # Initialize with default values if not found
                userconfig['budgets'] = {
                    'Food': "0.00",
                    'Transport': "0.00",
                    'Fuel': "0.00",
                    'Entertainment': "0.00",
                    'Utilities': "0.00",
                    'Shopping': "0.00",
                    'Groceries': "0.00",
                    'Health': "0.00",
                    'Other': "0.00",
                    'Final': "0.00"
                }

            # Update the input field labels
            update_currency_labels()
        else:
            print("No saved configuration found. Using defaults.")
    except Exception as e:
        print(f"Error loading configuration: {e}")

def update_currency_labels():
    """Updates the input field labels to display the currency values."""
    try:
        # Get the label elements
        home_currency_label = document.querySelector('label[for="amount_home"]')
        destination_currency_label = document.querySelector('label[for="amount_destination"]')

        # Update the labels
        if home_currency_label:
            home_currency_label.textContent = f"Home Currency Amount ({userconfig['home-currency']}):"

        if destination_currency_label:
            # Handle "None" as destination currency
            if userconfig['destination-currency'] == "None":
                destination_currency_label.textContent = "Destination Currency Amount (None):"
            else:
                destination_currency_label.textContent = f"Destination Currency Amount ({userconfig['destination-currency']}):"

        print(f"Updated currency labels: Home={userconfig['home-currency']}, Destination={userconfig['destination-currency']}")
    except Exception as e:
        print(f"Error updating currency labels: {e}")

async def initial_setup():
    """Sets default values, loads data, and renders initial state."""
    print("Running initial setup...")

    # Load regional partners data first
    await load_support_partners()

    # Load expense categories
    await load_expense_categories()

    load_user_config() # Load user configuration
    load_expenses() # Load expense data

    # Populate category dropdown if we're on the add-expense page
    populate_category_dropdown()

    # Check if we need to edit an expense (redirected from expense-cards.html)
    edit_id_str = js.localStorage.getItem("expense_to_edit")
    if edit_id_str:
        try:
            edit_id = int(edit_id_str)
            # Clear the localStorage item to prevent repeated editing
            js.localStorage.removeItem("expense_to_edit")
            # Check if we're on the add-expense.html page by checking for required form elements
            if description_main_input and date_input and category_input and payment_method_input:
                # Edit the expense
                edit_expense_item(edit_id)
            else:
                print("Not on add-expense.html page, skipping edit_expense_item call")
        except ValueError:
            print(f"Invalid expense ID to edit: {edit_id_str}")

    if date_input:
        try:
             date_input.value = datetime.now().strftime('%Y-%m-%d')
        except Exception as e:
            print(f"Could not set default date: {e}")
    else:
        print("Warning: Date input element not found during setup.")

    render_expenses() # Render BOTH views (cards and table)


# --- Run initial setup ---
asyncio.ensure_future(initial_setup())

print("Expense tracker initialized. Card view and table view enabled.")