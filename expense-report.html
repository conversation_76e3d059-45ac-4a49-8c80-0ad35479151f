<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Report</title>
    <link rel="stylesheet" href="https://pyscript.net/releases/2024.1.1/core.css">
    <script type="module" src="https://pyscript.net/releases/2024.1.1/core.js"></script>
    <style>
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f7f6;
            color: #333;
        }
        .report-header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .info-section {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info-section p {
            margin: 5px 0;
        }
        .card-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px; /* Space between cards */
            justify-content: center; /* Center cards if they don't fill the row */
        }
        .card {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            min-width: 400px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex-grow: 1; /* Allows cards to grow */
            flex-basis: 400px; /* Base width before growing */
        }
        .card h3 {
            margin-top: 0;
            color: #0056b3;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .card p {
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
        }
        .card p .label {
            color: #555;
        }
        .card p .value {
            font-weight: bold;
        }
        .card .category-total {
            font-weight: bold;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px dashed #ccc;
            color: #333;
        }
        .grand-totals {
            margin-top: 30px;
            padding: 20px;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
        }
        .grand-totals h2 {
            margin-top: 0;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="report-header">
        <h1>Expense Report</h1>
    </div>

    <div class="info-section">
        <h2>Report Summary</h2>
        <p>Home Currency (from config): <span id="home-currency-config">N/A</span></p>
        <p>Total Currencies Used in Expenses: <span id="currencies-count">0</span></p>
        <p>Currencies: <span id="currencies-list">None</span></p>
        <p>Total Expense Categories Used: <span id="categories-count">0</span></p>
        <p>Categories: <span id="categories-list">None</span></p>
    </div>

    <div id="report-container" class="card-container">
        <!-- Cards will be injected here by Pyscript -->
    </div>

    <div id="grand-totals-container" class="grand-totals">
        <h2>Grand Totals</h2>
        <!-- Grand totals per currency will be injected here -->
    </div>

    <py-config>
        packages = ["pyodide-http"]
    </py-config>

    <py-script src="expense-report.py"></py-script>

    <!-- Dummy Data for Testing (Remove or comment out in production) -->
    <script>
        // Ensure localStorage is clean for testing or set up dummy data
        // localStorage.clear(); // Uncomment to clear for a fresh test

        const USERCONFIG_STORAGE_KEY = 'userconfig';
        const EXPENSES_STORAGE_KEY = 'expenses_data'; // Match your app's key
        const CATEGORIES_JSON_PATH = './expense_categories.json'; // Path to your categories JSON

        // 1. Dummy userconfig
        if (!localStorage.getItem(USERCONFIG_STORAGE_KEY)) {
            const dummyUserConfig = {
                'home-currency': "USD",
                'destination-currency': "USD", // Default, may not be used for reporting if expenses specify
                'exchange-rate': "1.00",
                'budgets': { /* ... your budget structure ... */ },
                'running-balance': "0.00"
            };
            localStorage.setItem(USERCONFIG_STORAGE_KEY, JSON.stringify(dummyUserConfig));
            console.log("Dummy userconfig set.");
        }

        // 2. Dummy expense categories JSON (place this in a file named expense_categories.json)
        // Content for expense_categories.json (as provided in the prompt)
        /*
        {
            "Accommodation": ["Hotel", "Hostel", "Airbnb/Rental", "Camping", "ResortFees", "Other"],
            "Food": ["Groceries", "Restaurant/DiningOut", "Cafe/CoffeeShops", "Takeaway/Delivery", "Other"],
            "Transport": ["Flights", "Train", "Bus/Coach", "Fuel/Petrol/Diesel", "Other"],
            "Entertainment": ["Movies/Cinema", "Concerts/Gigs", "Museums/Galleries", "Other"]
        }
        */
        // Ensure you have this file accessible at CATEGORIES_JSON_PATH

        // 3. Dummy expenses data
        if (!localStorage.getItem(EXPENSES_STORAGE_KEY)) {
            const dummyExpenses = [
                { "id": "1", "date": "2023-10-01", "description": "Groceries", "category": "Food", "sub_category": "Groceries", "amount": "50.75", "home_currency_code": "USD", "destination_currency_code": "USD" },
                { "id": "2", "date": "2023-10-01", "description": "Dinner in Paris", "category": "Food", "sub_category": "Restaurant/DiningOut", "amount": "30.00", "home_currency_code": "USD", "destination_currency_code": "EUR" },
                { "id": "3", "date": "2023-10-02", "description": "Train Ticket London", "category": "Transport", "sub_category": "Train", "amount": "15.50", "home_currency_code": "GBP", "destination_currency_code": "GBP" },
                { "id": "4", "date": "2023-10-03", "description": "Hotel in Berlin", "category": "Accommodation", "sub_category": "Hotel", "amount": "120.00", "home_currency_code": "USD", "destination_currency_code": "EUR" },
                { "id": "5", "date": "2023-10-03", "description": "Coffee NYC", "category": "Food", "sub_category": "Cafe/CoffeeShops", "amount": "5.20", "home_currency_code": "USD", "destination_currency_code": "USD" },
                { "id": "6", "date": "2023-10-04", "description": "Museum Tickets London", "category": "Entertainment", "sub_category": "Museums/Galleries", "amount": "25.00", "home_currency_code": "GBP", "destination_currency_code": "GBP" },
                { "id": "7", "date": "2023-10-05", "description": "Snacks EUR airport", "category": "Food", "sub_category": "Other", "amount": "8.50", "home_currency_code": "USD", "destination_currency_code": "EUR" }
            ];
            localStorage.setItem(EXPENSES_STORAGE_KEY, JSON.stringify(dummyExpenses));
            console.log("Dummy expenses set.");
        }
    </script>
</body>
</html>