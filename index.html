<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Expense Tracker</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
  <link rel="stylesheet" href="https://pyscript.net/releases/2024.1.1/core.css" />
  <script type="module" src="https://pyscript.net/releases/2024.1.1/core.js"></script>
</head>
<body>
  <div class="app-container">
    <header class="header">
      <div class="container header-content">
        <div class="logo">ExpenseTracker</div>
        <div class="user-greeting">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="user-icon">
            <circle cx="12" cy="8" r="5"></circle>
            <path d="M20 21a8 8 0 1 0-16 0"></path>
          </svg>
          <span>Welcome Back, User!</span>
        </div>
      </div>
    </header>

    <main class="main-content">
      <section class="balance-section">
        <h2 class="section-title">Your Balance</h2>
        <p id="balance-amount" class="balance-amount">$0.00</p>
        <p id="balance-change" class="balance-change">$0.00 (Today)</p>
      </section>

      <section class="quick-actions-section">
        <div class="card">
          <h2 class="section-title">Quick Actions</h2>
          <div class="quick-actions-grid">
            <a href="add-expense.html" class="action-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-circle">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>
              </svg>
              <span>Add Expense</span>
            </a>
            <a href="userconfig.html" class="action-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-dollar-sign">
                <line x1="12" y1="1" x2="12" y2="23"></line>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
              <span>Currency/Budget</span>
            </a>
            <a href="data-management.html" class="action-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-download">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              <span>Export to CSV</span>
            </a>
             <button class="action-button" onclick="alert('Reports functionality not implemented yet.');">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="20" x2="18" y2="10"></line>
                <line x1="12" y1="20" x2="12" y2="4"></line>
                <line x1="6" y1="20" x2="6" y2="14"></line>
              </svg>
              <span>Reports</span>
            </button>
          </div>
        </div>
      </section>

      <section class="transactions-section">
        <h2 class="section-title">Recent Transactions</h2>
        <div id="expense-cards-container">
          <!-- Expense cards will be dynamically inserted here by PyScript -->
        </div>
        <div class="view-all-container">
          <a href="expense-cards.html" class="secondary-button">View All Transactions</a>
        </div>
      </section>

      <section class="image-display-section" id="image-section-1">
        <h2 class="section-title" id="home-currency-title">Home Currency</h2>
        <div id="dynamic-image-container-home-currency"></div>
      </section>

      <section class="image-display-section" id="image-section-2">
        <h2 class="section-title" id="destination-currency-title">Destination Currency</h2>
        <div id="dynamic-image-container-destination-currency"></div>
      </section>

      <section class="image-display-section" id="image-section-3">
        <h2 class="section-title">Spotlight on :</h2>
        <div id="dynamic-image-container-featured-image-1"></div>
      </section>

      <section class="expenses-list-section">
        <h2 class="section-title">Expenses List</h2>
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <button id="export-button" class="primary-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 5px;">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            Export to CSV
          </button>
          <div class="table-info" style="font-size: 0.8rem; color: #666;">
            <span id="expense-count">0</span> expenses found
          </div>
        </div>
        <div class="table-container">
          <table id="expense-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Main Description</th>
                <th>Additional Description</th>
                <th>Home Amount</th>
                <th>Home Currency</th>
                <th>Destination Amount</th>
                <th>Destination Currency</th>
                <th>Exchange Rate</th>
                <th>Category</th>
                <th>Category Budget</th>
                <th>Payment Method</th>
                <th>Location</th>
                <th>Tags</th>
                <th>Expense Goal</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody id="expense-list">
              <!-- Table rows will be dynamically inserted here by PyScript -->
            </tbody>
          </table>
        </div>
        <div id="status_area"></div>
      </section>
    </main>

    <nav class="bottom-nav">
      <div class="container nav-content">
        <a href="index.html" class="nav-item active">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          <span>Home</span>
        </a>
        <a href="add-expense.html" class="nav-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="16"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
            <span>Add Expense</span>
        </a>
        <a href="expense-cards.html" class="nav-item active">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="2" y="5" width="20" height="14" rx="2"></rect>
              <line x1="2" y1="10" x2="22" y2="10"></line>
            </svg>
            <span>All Expenses</span>
        </a>
        <a href="settings.html" class="nav-item">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            <span>Settings</span>
        </a>
      </div>
    </nav>
  </div>

  <script type="py" src="./main.py"></script>

  <script>
    // Add shadow indicators for horizontal scroll
    document.addEventListener('DOMContentLoaded', function() {
        const tableContainer = document.querySelector('.table-container');
        if (!tableContainer) return; // Exit if table container not found

        function updateShadows() {
            // Recalculate maxScroll inside the function as table width might change
            const maxScroll = tableContainer.scrollWidth - tableContainer.clientWidth;
            const currentScroll = tableContainer.scrollLeft;

            // Add a tolerance to avoid flickering shadows at the very edges
            const tolerance = 1;

            if (maxScroll > tolerance) { // Only show shadows if there's actual scrollable area
                tableContainer.classList.toggle('shadow-left', currentScroll > tolerance);
                tableContainer.classList.toggle('shadow-right', currentScroll < (maxScroll - tolerance));
            } else {
                 // Remove shadows if no longer scrollable
                 tableContainer.classList.remove('shadow-left', 'shadow-right');
            }
        }

        // Debounce function to limit how often updateShadows runs
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        const debouncedUpdateShadows = debounce(updateShadows, 50);

        tableContainer.addEventListener('scroll', debouncedUpdateShadows);
        window.addEventListener('resize', debouncedUpdateShadows);

         // Use MutationObserver to detect changes in table content (e.g., PyScript updates)
         const observer = new MutationObserver(debouncedUpdateShadows);
         const tableBody = tableContainer.querySelector('tbody');
         if(tableBody) {
             observer.observe(tableBody, { childList: true, subtree: true });
         }

        // Initial check
        updateShadows();
    });
  </script>
  </body>
</html>