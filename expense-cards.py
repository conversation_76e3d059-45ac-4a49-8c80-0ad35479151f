import js
from pyodide.ffi import create_proxy
from datetime import datetime
from pyscript import document
import json
import html
import io
import csv
from pyodide.http import pyfetch
import asyncio

# --- Constants ---
EXPENSES_STORAGE_KEY = "pyscript_expense_tracker_data"
USER_CONFIG_KEY = "pyscript_expense_tracker_config"
REGIONAL_PARTNERS_FILE = "support-partners.json"  # Keeping the same filename for backward compatibility

# --- Global State ---
expenses = [] # List to store expense dictionaries (will be loaded from localStorage)
current_edit_id = None # Track the ID of the expense being edited
userconfig = {
    'home-currency': "USD",
    'destination-currency': "USD",
    'exchange-rate': "1.00",
    'budgets': {
        'Food': "0.00",
        'Transport': "0.00",
        'Fuel': "0.00",
        'Entertainment': "0.00",
        'Utilities': "0.00",
        'Shopping': "0.00",
        'Groceries': "0.00",
        'Health': "0.00",
        'Other': "0.00",
        'Final': "0.00"
    }
} # Store currency names, exchange rate, and budget amounts
support_partners = {} # Dictionary to store support partners data from JSON file

# --- DOM Element References ---
expense_cards_container = document.getElementById("expense-cards-container")
status_area = document.getElementById("status_area")
import_button = document.getElementById("import-button")
csv_file_input = document.getElementById("csv-file-input")
delete_all_button = document.getElementById("delete-all-button")

# Function calculate_total_expenses removed as per requirements

# --- Helper function to get icon HTML ---
def get_icon_html(category):
    """Returns HTML for an icon based on the expense category."""
    category_lower = category.lower() if category else 'other'
    icon_path_prefix = "./icons/" # ASSUMPTION: You have an 'icons' folder next to index.html

    # Using simple <img> tags. Replace paths/styles as needed.
    if 'fuel' in category_lower:
        return f'<img src="{icon_path_prefix}fuel.png" alt="Fuel" title="{category}" style="width:40px; height:40px;">'
    elif 'food' in category_lower or 'groceries' in category_lower or 'restaurant' in category_lower:
            return f'<img src="{icon_path_prefix}food.png" alt="Food/Groceries" title="{category}" style="width:40px; height:40px;">'
    elif 'transport' in category_lower:
            return f'<img src="{icon_path_prefix}transport.png" alt="Transport" title="{category}" style="width:40px; height:40px;">'
    elif 'shopping' in category_lower:
            return f'<img src="{icon_path_prefix}shopping.png" alt="Shopping" title="{category}" style="width:40px; height:40px;">'
    elif 'utilities' in category_lower:
            return f'<img src="{icon_path_prefix}utilities.png" alt="Utilities" title="{category}" style="width:40px; height:40px;">'
    elif 'entertainment' in category_lower:
            return f'<img src="{icon_path_prefix}entertainment.png" alt="Entertainment" title="{category}" style="width:40px; height:40px;">'
    elif 'health' in category_lower:
            return f'<img src="{icon_path_prefix}health.png" alt="Health" title="{category}" style="width:40px; height:40px;">'
    else: # Default icon
            return f'<img src="{icon_path_prefix}default.png" alt="Expense" title="{category}" style="width:40px; height:40px;">'
            # Alternative default using SVG:
            # return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="40px" height="40px" style="opacity: 0.5;"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/></svg>'

# --- Functions ---
def load_expenses():
    """Loads expenses from localStorage on startup."""
    global expenses # We need to modify the global list
    try:
        stored_data = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
        if stored_data:
            print("Found saved data in localStorage. Loading...") # Console log
            loaded_list = json.loads(stored_data)
            temp_expenses = []
            for i, item in enumerate(loaded_list):
                try:
                    date_obj = datetime.strptime(item['date'], '%Y-%m-%d').date()
                    # Reconstruct the description if necessary or use stored one
                    # Handle both new and old variable names for backward compatibility
                    desc_main = item.get('description_main', item.get('description_primary', ''))
                    desc_additional = item.get('description_additional', item.get('description_secondary', ''))
                    description = f"{desc_main} - {desc_additional}" if desc_additional else desc_main

                    # Get the category and its budget
                    category = item.get('category', 'Other')
                    category_budget = item.get('category_budget', userconfig['budgets'].get(category, "0.00"))

                    temp_expenses.append({
                        'id': int(item.get('exp_id', i + 1)), # Use saved ID or generate
                        'desc_main': desc_main,
                        'desc_additional': desc_additional,
                        'description': description,
                        'amount_home': float(item.get('amount_home', item.get('amount', 0.0))), # Backward compatibility
                        'amount_destination': float(item.get('amount_destination', 0.0)),
                        'currency_home': item.get('currency_home', userconfig['home-currency']),  # Load stored currency or use default
                        'currency_destination': item.get('currency_destination', userconfig['destination-currency']),  # Load stored currency or use default
                        'expense_roe': item.get('expense_roe', userconfig['exchange-rate']),  # Load stored exchange rate or use default
                        'date': item['date'],
                        'date_obj': date_obj, # Store the date object for sorting
                        'category': category,
                        'category_budget': category_budget,  # Load stored budget or use default
                        'payment_method': item.get('payment_method', ''),
                        'location': item.get('location', ''),
                        'tags': item.get('tags', []), # Assume tags are stored as a list
                        'expense_goal': item.get('expense_goal', ''),
                        'support_partner': item.get('support_partner', '')
                    })
                except (ValueError, KeyError, TypeError) as parse_err:
                        print(f"Skipping corrupted item during load: {item}, Error: {parse_err}")
            expenses = temp_expenses # Assign loaded data to global variable
            print(f"Loaded {len(expenses)} expenses.")
        else:
            print("No saved data found in localStorage.")
            expenses = [] # Ensure it's an empty list if nothing is loaded
    except Exception as e:
        print(f"Error loading expenses from localStorage: {e}")
        expenses = []
        show_status("Could not load saved data. Starting fresh.", is_success=False)

def show_status(message, is_success=True):
    """Displays a status message to the user."""
    if not status_area:
            print(f"Status Error: Cannot find status area element. Message: {message}")
            js.alert(f"Status: {message}") # Fallback to alert if status area missing
            return

    status_area.innerHTML = ""
    div = document.createElement("div")
    div.textContent = message
    div.classList.add("status-message")
    div.classList.add("success" if is_success else "error")
    status_area.appendChild(div)
    # Clear message after 3 seconds using setTimeout from js module
    clear_proxy = create_proxy(lambda: status_area.removeChild(div) if status_area.contains(div) else None)
    js.setTimeout(clear_proxy, 3000)

def render_expense_cards():
    """Renders the expense cards view."""
    if not expense_cards_container:
        print("Error: Cannot find expense cards container (ID: expense-cards-container).")
        return

    # Clear previous content
    expense_cards_container.innerHTML = ""

    # Handle case where there are no expenses
    if not expenses:
        # Add message to card container
        no_cards_msg = document.createElement("p")
        no_cards_msg.textContent = "No expenses recorded yet."
        no_cards_msg.style.textAlign = "center"
        no_cards_msg.style.color = "#666"
        expense_cards_container.appendChild(no_cards_msg)
        return # Exit early

    # Sort expenses by ID only (highest ID first)
    # This ensures that the most recently added expenses are always at the top
    sorted_expenses = sorted(expenses, key=lambda x: -x.get('id', 0))

    all_cards_html = "" # Accumulate card HTML strings

    for expense in sorted_expenses:
        tags_str = ', '.join(expense.get('tags', [])) if isinstance(expense.get('tags'), list) else expense.get('tags', '-')

        # Create Card HTML
        icon_html = get_icon_html(expense['category']) # Get the icon
        card_html = f"""
        <div class="expense-card" data-expense-id="{expense.get('id', '')}">
            <div class="card-icon">
                {icon_html}
            </div>
            <div class="card-details">
                <p><strong>{html.escape(expense['desc_main'])}</strong></p>
                <p><strong>Additional Description:</strong> {html.escape(expense.get('desc_additional', '-'))}</p>
                <p><strong>Date:</strong> {html.escape(expense['date'])}</p>
                <p>
                    <strong>{expense.get('currency_home', userconfig['home-currency'])}:</strong>
                    <span style="font-weight: bold;">{expense.get('amount_home', 0.0):.2f}</span> |
                    <strong>{expense.get('currency_destination', userconfig['destination-currency'])}:</strong>
                    <span style="font-weight: bold;">{expense.get('amount_destination', 0.0):.2f}</span>
                </p>
                <p><strong>Exchange Rate:</strong> {expense.get('expense_roe', userconfig['exchange-rate'])}</p>
                <p>
                    <strong>Category:</strong> {html.escape(expense['category'])} |
                    <strong>Budget:</strong> {expense.get('category_budget', "0.00")}
                </p>
                <p>
                    <strong>Method:</strong> {html.escape(expense['payment_method'])} |
                    <strong>Location:</strong> {html.escape(expense.get('location', '-'))}
                </p>
                <p><strong>Tags:</strong> {html.escape(tags_str)}</p>
                <p><strong>Expense Goal:</strong> {html.escape(expense.get('expense_goal', '-'))}</p>
                <p><strong>Regional Partner:</strong> {html.escape(expense.get('support_partner', '-'))}</p>
                <p>
                    <button class="card-edit-button" data-id="{expense.get('id', '')}">Edit</button>
                    <button class="card-delete-button" data-id="{expense.get('id', '')}">Delete</button>
                </p>
            </div>
        </div>
        """
        all_cards_html += card_html # Append to the accumulated string

    # Update DOM after loop
    expense_cards_container.innerHTML = all_cards_html # Set the card container content

    # Add event listeners for buttons on CARDS
    card_edit_buttons = expense_cards_container.querySelectorAll(".card-edit-button")
    for button in card_edit_buttons:
            item_id_str = button.getAttribute("data-id")
            if item_id_str:
                item_id = int(item_id_str)
                # Create proxy specific to this button's ID
                card_edit_proxy = create_proxy(lambda _event, current_id=item_id: edit_expense_item(current_id))
                button.addEventListener("click", card_edit_proxy)
            else:
                print(f"Warning: Card edit button found without data-id attribute.")

    card_delete_buttons = expense_cards_container.querySelectorAll(".card-delete-button")
    for button in card_delete_buttons:
            item_id_str = button.getAttribute("data-id")
            if item_id_str:
                item_id = int(item_id_str)
                # Create proxy specific to this button's ID
                card_delete_proxy = create_proxy(lambda _event, current_id=item_id: delete_expense_item(current_id))
                button.addEventListener("click", card_delete_proxy)
            else:
                print(f"Warning: Card delete button found without data-id attribute.")

    # Total expense calculations removed as per requirements

def edit_expense_item(item_id):
    """Redirects to the add expense page for editing."""
    # Store the ID to edit in localStorage
    js.localStorage.setItem("expense_to_edit", str(item_id))
    # Redirect to the add expense page instead of index.html
    js.window.location.href = "add-expense.html"

def delete_expense_item(item_id):
    """Deletes an expense item by its unique ID."""
    global expenses
    original_length = len(expenses)
    # Filter out the expense with the matching ID
    expenses = [exp for exp in expenses if exp.get('id') != item_id]

    if len(expenses) < original_length:
        # Save to localStorage
        try:
            # Prepare data for saving
            expenses_to_save = []
            for i, exp in enumerate(expenses):
                expenses_to_save.append({
                    'exp_id': exp.get('id', i + 1),
                    'description_main': exp.get('desc_main', ''),
                    'description_additional': exp.get('desc_additional', ''),
                    'description': exp.get('description', ''),
                    'amount_home': exp.get('amount_home', exp.get('amount', 0.0)),
                    'amount_destination': exp.get('amount_destination', 0.0),
                    'date': exp.get('date', ''),
                    'currency_home': exp.get('currency_home', userconfig['home-currency']),
                    'currency_destination': exp.get('currency_destination', userconfig['destination-currency']),
                    'expense_roe': exp.get('expense_roe', userconfig['exchange-rate']),
                    'category': exp.get('category', 'Other'),
                    'category_budget': exp.get('category_budget', "0.00"),
                    'payment_method': exp.get('payment_method', ''),
                    'location': exp.get('location', ''),
                    'tags': exp.get('tags', []),
                    'expense_goal': exp.get('expense_goal', ''),
                    'support_partner': exp.get('support_partner', '')
                })

            expenses_json = json.dumps(expenses_to_save)
            js.localStorage.setItem(EXPENSES_STORAGE_KEY, expenses_json)
            print(f"Saved {len(expenses)} expenses to localStorage.")
        except Exception as e:
            print(f"Error saving expenses to localStorage: {e}")
            show_status("Error saving data. Changes might be lost on refresh.", is_success=False)

        show_status("Expense deleted.", is_success=True)
    else:
        print(f"Error: Could not find expense with ID {item_id} to delete.")
        show_status("Error: Could not find the expense to delete.", is_success=False)

    render_expense_cards() # Re-render the cards

def delete_all_expenses(_event):
    """Deletes all expense records from localStorage after confirmation."""
    if js.confirm("Confirm deletion of all expense records"):
        global expenses
        try:
            # Clear the expenses list
            expenses = []
            # Remove from localStorage
            js.localStorage.removeItem(EXPENSES_STORAGE_KEY)
            print("All expenses deleted from localStorage.")
            show_status("All expenses deleted successfully.", is_success=True)
            render_expense_cards() # Re-render the cards
        except Exception as e:
            print(f"Error deleting all expenses: {e}")
            show_status(f"Error deleting all expenses: {e}", is_success=False)



# --- Initial Setup ---
def import_csv_data(csv_text):
    """Imports expense data from a CSV string.

    Args:
        csv_text: CSV data as a string

    Returns:
        list: Imported expense data
        str: Status message
    """
    try:
        # Use StringIO to treat the string as a file
        csv_buffer = io.StringIO(csv_text)

        # Skip BOM if present
        if csv_text.startswith('\ufeff'):
            csv_buffer = io.StringIO(csv_text[1:])

        # Create a CSV reader
        reader = csv.reader(csv_buffer)

        # Read the header row
        header = next(reader)
        print(f"CSV Header: {header}")

        # Print the raw CSV data for debugging
        print(f"Raw CSV data (first 200 chars): {csv_text[:200]}")

        # Map header indices
        header_map = {}
        expected_headers = [
            'ID', 'Date', 'Primary Description', 'Additional Description',
            'Home Currency Amount', 'Home Currency',
            'Destination Currency Amount', 'Destination Currency',
            'Exchange Rate',
            'Category', 'Category Budget', 'Payment Method', 'Location', 'Tags',
            'Expense Goal', 'Regional Partner'
        ]

        # Also check for old header format and handle variations
        old_headers = {
            'Amount': 'Home Currency Amount',
            'Primary Currency': 'Home Currency',  # Map to new column name
            'Secondary Currency': 'Destination Currency',  # Map to new column name
            'Secondary Description': 'Additional Description',  # Map old header to new name
            'Support Partner': 'Regional Partner',  # Map old header to new name
            'ID': None,  # Ignore this column
            'Home Currency': 'Home Currency',  # Ensure exact match
            'Destination Currency': 'Destination Currency'  # Ensure exact match
        }

        # Map header indices
        for i, col in enumerate(header):
            # Check for direct match
            if col in expected_headers:
                header_map[col] = i
                print(f"Found header: '{col}' at index {i}")
            # Check for old header format
            elif col in old_headers and old_headers[col] is not None:
                header_map[old_headers[col]] = i
                print(f"Mapped old header: '{col}' to '{old_headers[col]}' at index {i}")
            else:
                print(f"Ignoring unknown header: '{col}' at index {i}")

        # Print the final header map
        print("Final header map:")
        for header_name, index in header_map.items():
            print(f"  {header_name}: {index}")

        # Validate that we have the minimum required headers
        required_headers = ['Date', 'Primary Description']
        missing_headers = [h for h in required_headers if h not in header_map]
        if missing_headers:
            print(f"Missing headers: {missing_headers}")
            print(f"Found headers: {header}")
            return [], f"Missing required headers: {', '.join(missing_headers)}"

        # Parse the data rows
        imported_expenses = []
        for row_num, row in enumerate(reader, start=2):  # Start at 2 to account for header row
            try:
                # Skip empty rows
                if not row or all(cell.strip() == '' for cell in row):
                    continue

                # Extract data using the header map
                expense_data = {}

                # Get date
                date_str = row[header_map['Date']].strip()
                try:
                    # Try different date formats
                    try:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                    except ValueError:
                        # Try alternative format with +AC0- encoding
                        date_str = date_str.replace('+AC0-', '-')
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                except ValueError:
                    print(f"Warning: Invalid date format in row {row_num}: {date_str}")
                    continue

                # Get descriptions
                desc_main = row[header_map['Primary Description']].strip()
                # Replace encoded characters
                desc_main = desc_main.replace('+AC0-', '-')

                desc_additional = ''
                if 'Additional Description' in header_map and header_map.get('Additional Description', -1) < len(row):
                    desc_additional = row[header_map.get('Additional Description', -1)].strip()
                    # Replace encoded characters
                    desc_additional = desc_additional.replace('+AC0-', '-')

                # Get amounts
                amount_home = 0.0
                if 'Home Currency Amount' in header_map and header_map['Home Currency Amount'] < len(row):
                    amount_home_str = row[header_map['Home Currency Amount']].strip()
                    try:
                        amount_home = float(amount_home_str) if amount_home_str else 0.0
                    except ValueError:
                        print(f"Warning: Invalid home amount format in row {row_num}: '{amount_home_str}'")

                amount_destination = 0.0
                if 'Destination Currency Amount' in header_map and header_map['Destination Currency Amount'] < len(row):
                    amount_destination_str = row[header_map['Destination Currency Amount']].strip()
                    try:
                        amount_destination = float(amount_destination_str) if amount_destination_str else 0.0
                    except ValueError:
                        print(f"Warning: Invalid destination amount format in row {row_num}: '{amount_destination_str}'")

                # Print row data for debugging
                print(f"Row {row_num}: Date={date_str}, Desc={desc_main}, Home={amount_home}, Dest={amount_destination}")

                # Get currency values if available
                currency_home = userconfig['home-currency']
                if 'Home Currency' in header_map and header_map['Home Currency'] < len(row):
                    currency_home_str = row[header_map['Home Currency']].strip()
                    print(f"  Home Currency in CSV: '{currency_home_str}'")
                    if currency_home_str:
                        currency_home = currency_home_str
                        print(f"  Using Home Currency from CSV: {currency_home}")
                    else:
                        print(f"  Using default Home Currency: {currency_home}")
                else:
                    print(f"  'Home Currency' column not found in CSV, using default: {currency_home}")

                currency_destination = userconfig['destination-currency']
                if 'Destination Currency' in header_map and header_map['Destination Currency'] < len(row):
                    currency_destination_str = row[header_map['Destination Currency']].strip()
                    print(f"  Destination Currency in CSV: '{currency_destination_str}'")
                    if currency_destination_str:
                        currency_destination = currency_destination_str
                        print(f"  Using Destination Currency from CSV: {currency_destination}")
                    else:
                        print(f"  Using default Destination Currency: {currency_destination}")
                else:
                    print(f"  'Destination Currency' column not found in CSV, using default: {currency_destination}")

                # Ensure at least one amount is provided
                if amount_home == 0.0 and amount_destination == 0.0:
                    print(f"Warning: No valid amount provided in row {row_num}")
                    continue

                # Get other fields
                category = row[header_map.get('Category', -1)].strip() if 'Category' in header_map and header_map.get('Category', -1) < len(row) else 'Other'

                # Get category budget if available
                # First, check if it's in the CSV file
                category_budget = "0.00"
                if 'Category Budget' in header_map and header_map['Category Budget'] < len(row):
                    category_budget_str = row[header_map['Category Budget']].strip()
                    if category_budget_str:
                        category_budget = category_budget_str
                        print(f"  Using Category Budget from CSV: {category_budget}")
                    else:
                        # If not in CSV, use the budget from userconfig
                        category_budget = userconfig['budgets'].get(category, "0.00")
                        print(f"  Using Category Budget from userconfig: {category_budget}")
                else:
                    # If Category Budget column doesn't exist, use the budget from userconfig
                    category_budget = userconfig['budgets'].get(category, "0.00")
                    print(f"  'Category Budget' column not found in CSV, using from userconfig: {category_budget}")

                payment_method = row[header_map.get('Payment Method', -1)].strip() if 'Payment Method' in header_map and header_map.get('Payment Method', -1) < len(row) else ''
                location = row[header_map.get('Location', -1)].strip() if 'Location' in header_map and header_map.get('Location', -1) < len(row) else ''
                tags_str = row[header_map.get('Tags', -1)].strip() if 'Tags' in header_map and header_map.get('Tags', -1) < len(row) else ''
                tags = [tag.strip() for tag in tags_str.split(',')] if tags_str else []



                # Get exchange rate if available
                exchange_rate = userconfig['exchange-rate']
                if 'Exchange Rate' in header_map and header_map['Exchange Rate'] < len(row):
                    exchange_rate_str = row[header_map['Exchange Rate']].strip()
                    if exchange_rate_str:
                        exchange_rate = exchange_rate_str

                # Get expense goal and support partner if available
                expense_goal = ''
                if 'Expense Goal' in header_map and header_map['Expense Goal'] < len(row):
                    expense_goal = row[header_map['Expense Goal']].strip()

                # Get regional partner based on the destination currency
                # If destination currency is "None", use the home currency instead
                if currency_destination == "None":
                    support_partner = get_support_partner(currency_home)
                else:
                    support_partner = get_support_partner(currency_destination)

                # Override with the value from CSV if provided
                if 'Regional Partner' in header_map and header_map['Regional Partner'] < len(row) and row[header_map['Regional Partner']].strip():
                    support_partner = row[header_map['Regional Partner']].strip()
                # For backward compatibility, also check for Support Partner
                elif 'Support Partner' in header_map and header_map['Support Partner'] < len(row) and row[header_map['Support Partner']].strip():
                    support_partner = row[header_map['Support Partner']].strip()

                # Create the expense data dictionary
                expense_data = {
                    'desc_main': desc_main,
                    'desc_additional': desc_additional,
                    'description': f"{desc_main} - {desc_additional}" if desc_additional else desc_main,
                    'amount_home': amount_home,
                    'amount_destination': amount_destination,
                    'currency_home': currency_home,
                    'currency_destination': currency_destination,
                    'expense_roe': exchange_rate,
                    'date': date_str,
                    'date_obj': date_obj,
                    'category': category,
                    'category_budget': category_budget,
                    'payment_method': payment_method,
                    'location': location,
                    'tags': tags,
                    'expense_goal': expense_goal,
                    'support_partner': support_partner
                }

                # Add ID if available
                if 'ID' in header_map and header_map['ID'] < len(row) and row[header_map['ID']].strip():
                    try:
                        expense_data['id'] = int(row[header_map['ID']])
                        print(f"  Using ID from CSV: {expense_data['id']}")
                    except ValueError:
                        print(f"  Invalid ID in CSV: '{row[header_map['ID']]}'")
                        pass  # Ignore invalid IDs

                imported_expenses.append(expense_data)

            except Exception as row_error:
                print(f"Error processing row {row_num}: {row_error}")
                continue

        return imported_expenses, f"Successfully imported {len(imported_expenses)} expenses."

    except Exception as e:
        print(f"Error importing CSV: {e}")
        return [], f"Error importing CSV: {e}"

def handle_file_import(event):
    """Handles the file import process."""
    global expenses

    # Get the selected file
    try:
        # Access files using JavaScript property access
        files = event.target.files
        file = files.item(0) if files.length > 0 else None
        print(f"Selected file: {file.name if file else 'None'}")
    except Exception as e:
        print(f"Error accessing file: {e}")
        show_status(f"Error accessing file: {e}", is_success=False)
        return

    if not file:
        show_status("No file selected.", is_success=False)
        return

    # Check file type
    if not file.name.endswith('.csv'):
        show_status("Please select a CSV file.", is_success=False)
        return

    # Create a FileReader to read the file
    reader = js.FileReader.new()

    # Define what happens when the file is loaded
    def on_load(e):
        try:
            # Get the file content as text
            csv_text = e.target.result
            print(f"File content (first 200 chars): {csv_text[:200]}")

            # Import the CSV data
            imported_expenses, message = import_csv_data(csv_text)

            if not imported_expenses:
                show_status(message, is_success=False)
                return

            # Ask for confirmation before importing
            if js.confirm(f"{message}\n\nDo you want to add these expenses to your existing data?"):
                # Generate new IDs for imported expenses to avoid conflicts
                max_id = max([exp.get('id', 0) for exp in expenses] + [0])
                for i, exp in enumerate(imported_expenses):
                    if 'id' not in exp:
                        exp['id'] = max_id + i + 1

                # Add the imported expenses to the existing expenses
                expenses.extend(imported_expenses)

                # Save the updated expenses to localStorage
                save_expenses()

                # Render the updated expenses
                render_expense_cards()

                show_status(f"Successfully imported {len(imported_expenses)} expenses.", is_success=True)
            else:
                show_status("Import cancelled.", is_success=False)

        except Exception as e:
            print(f"Error handling file import: {e}")
            show_status(f"Error importing file: {e}", is_success=False)

    # Set up the FileReader event handlers
    on_load_proxy = create_proxy(on_load)
    reader.onload = on_load_proxy

    # Read the file as text with UTF-8 encoding
    reader.readAsText(file, 'UTF-8')

def save_expenses():
    """Saves the current expenses list to localStorage."""
    try:
        # Prepare data for saving
        expenses_to_save = []
        for i, exp in enumerate(expenses):
            expenses_to_save.append({
                'exp_id': exp.get('id', i + 1),
                'description_main': exp.get('desc_main', ''),
                'description_additional': exp.get('desc_additional', ''),
                'description': exp.get('description', ''),
                'amount_home': exp.get('amount_home', exp.get('amount', 0.0)),
                'amount_destination': exp.get('amount_destination', 0.0),
                'date': exp.get('date', ''),
                'currency_home': exp.get('currency_home', userconfig['home-currency']),
                'currency_destination': exp.get('currency_destination', userconfig['destination-currency']),
                'expense_roe': exp.get('expense_roe', userconfig['exchange-rate']),
                'category': exp.get('category', 'Other'),
                'category_budget': exp.get('category_budget', "0.00"),
                'payment_method': exp.get('payment_method', ''),
                'location': exp.get('location', ''),
                'tags': exp.get('tags', []),
                'expense_goal': exp.get('expense_goal', ''),
                'support_partner': exp.get('support_partner', '')
            })

        expenses_json = json.dumps(expenses_to_save)
        js.localStorage.setItem(EXPENSES_STORAGE_KEY, expenses_json)
        print(f"Saved {len(expenses)} expenses to localStorage.")
    except Exception as e:
        print(f"Error saving expenses to localStorage: {e}")
        show_status("Error saving data. Changes might be lost on refresh.", is_success=False)

async def load_support_partners():
    """Loads the regional partners data from the JSON file."""
    global support_partners
    try:
        response = await pyfetch(REGIONAL_PARTNERS_FILE)
        if response.status == 200:
            support_partners = await response.json()
            print("Loaded regional partners:", support_partners)
            return True
        else:
            print(f"Error loading regional partners: HTTP {response.status}")
            return False
    except Exception as e:
        print(f"Error loading regional partners: {e}")
        return False

def get_support_partner(currency):
    """Gets the regional partner for a given currency.

    If the currency is "None", use the Home Currency instead.
    """
    # If currency is None or "None", use the Home Currency instead
    if not currency or currency == "None":
        home_currency = userconfig['home-currency']
        if home_currency in support_partners:
            return support_partners[home_currency]
        return f"Unknown Regional Partner for {home_currency}"

    # Otherwise, use the provided currency
    if currency in support_partners:
        return support_partners[currency]
    return f"Unknown Regional Partner for {currency}"

def load_user_config():
    """Loads user configuration from localStorage."""
    global userconfig
    try:
        stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
        if stored_config:
            config = json.loads(stored_config)
            print("Loaded user configuration:", config)

            # Update the global userconfig
            if 'home-currency' in config:
                userconfig['home-currency'] = config['home-currency']

            if 'destination-currency' in config:
                userconfig['destination-currency'] = config['destination-currency']

            if 'exchange-rate' in config:
                userconfig['exchange-rate'] = config['exchange-rate']
            else:
                userconfig['exchange-rate'] = "1.00"  # Default value

            # Load budget amounts if available
            if 'budgets' in config:
                userconfig['budgets'] = config['budgets']
            else:
                # Initialize with default values if not found
                userconfig['budgets'] = {
                    'Food': "0.00",
                    'Transport': "0.00",
                    'Fuel': "0.00",
                    'Entertainment': "0.00",
                    'Utilities': "0.00",
                    'Shopping': "0.00",
                    'Groceries': "0.00",
                    'Health': "0.00",
                    'Other': "0.00",
                    'Final': "0.00"
                }

        else:
            print("No saved configuration found. Using defaults.")
    except Exception as e:
        print(f"Error loading configuration: {e}")

async def initial_setup():
    """Loads data and renders initial state."""
    print("Running initial setup for expense cards page...")

    # Load support partners data first
    await load_support_partners()

    load_user_config() # Load user configuration
    load_expenses() # Load data

    # Set up event listeners for import functionality
    if import_button and csv_file_input:
        import_button_click_proxy = create_proxy(lambda _event: csv_file_input.click())
        import_button.addEventListener("click", import_button_click_proxy)

        file_input_change_proxy = create_proxy(handle_file_import)
        csv_file_input.addEventListener("change", file_input_change_proxy)

    # Set up event listeners for delete buttons
    if delete_all_button:
        delete_all_proxy = create_proxy(delete_all_expenses)
        delete_all_button.addEventListener("click", delete_all_proxy)

    render_expense_cards() # Render the cards

# --- Run initial setup ---
asyncio.ensure_future(initial_setup())

print("Expense cards page initialized.")
