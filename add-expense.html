<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Expense - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://pyscript.net/releases/2024.1.1/core.css" />
    <script type="module" src="https://pyscript.net/releases/2024.1.1/core.js"></script>
</head>
<body>
  <div class="app-container">
    <header class="header">
      <div class="container header-content">
        <div class="logo">ExpenseTracker</div>
        <div class="user-greeting">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="user-icon">
            <circle cx="12" cy="8" r="5"></circle>
            <path d="M20 21a8 8 0 1 0-16 0"></path>
          </svg>
          <span>Welcome Back, User!</span>
        </div>
      </div>
    </header>

    <main class="main-content">
      <div class="card">
        <h2 class="section-title">Add New Expense :</h2>

        <form id="expense-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="desc_main">Primary Description:</label>
                    <input type="text" id="desc_main" placeholder="Main expense description" required>
                </div>
                <div class="form-group">
                    <label for="desc_additional">Additional Description:</label>
                    <input type="text" id="desc_additional" placeholder="Additional details">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="amount_home">Home Currency Amount:</label>
                    <input type="number" id="amount_home" placeholder="e.g., 15.50" step="0.01" min="0">
                </div>
                <div class="form-group">
                    <label for="amount_destination">Destination Currency Amount:</label>
                    <input type="number" id="amount_destination" placeholder="e.g., 17.25" step="0.01" min="0">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="date">Date:</label>
                    <input type="date" id="date" required>
                </div>
                <div class="form-group">
                    <label for="category">Category:</label>
                    <select id="category" required>
                        <option value="">Select Category</option>
                        <option value="Food">Food</option>
                        <option value="Transport">Transport</option>
                        <option value="Fuel">Fuel</option>
                        <option value="Entertainment">Entertainment</option>
                        <option value="Utilities">Utilities</option>
                        <option value="Shopping">Shopping</option>
                        <option value="Groceries">Groceries</option>
                        <option value="Health">Health</option>
                        <option value="Other">Other</option>
                        <option value="Final">Final</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="payment_method">Payment Method:</label>
                    <select id="payment_method" required>
                        <option value="">Select Payment Method</option>
                        <option value="Cash">Cash</option>
                        <option value="Credit Card">Credit Card</option>
                        <option value="Debit Card">Debit Card</option>
                        <option value="Bank Transfer">Bank Transfer</option>
                        <option value="EFT">EFT</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="location">Location:</label>
                    <input type="text" id="location" placeholder="Where was this expense made?">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group" style="flex-grow: 2;">
                    <label for="tags">Tags (comma-separated):</label>
                    <input type="text" id="tags" class="tags-input" placeholder="e.g., business, travel, lunch">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group" style="flex-grow: 2;">
                    <label for="expense_goal">Expense Goal:</label>
                    <input type="text" id="expense_goal" placeholder="Purpose or goal of this expense">
                </div>
            </div>
            <button type="submit">Add Expense</button>
        </form>

        <div id="status_area"></div>

        <div class="view-all-container" style="margin-top: 20px;">
            <a href="index.html" class="secondary-button">Back to Home</a>
            <a href="expense-cards.html" class="secondary-button">View All Expenses</a>
        </div>
      </div>
    </main>

    <nav class="bottom-nav">
      <div class="container nav-content">
        <a href="index.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          <span>Home</span>
        </a>
        <a href="userconfig.html" class="nav-item active">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="16"></line>
            <line x1="8" y1="12" x2="16" y2="12"></line>
          </svg>
          <span>Currency Settings</span>
        </a>
        <a href="expense-cards.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="2" y="5" width="20" height="14" rx="2"></rect>
            <line x1="2" y1="10" x2="22" y2="10"></line>
          </svg>
          <span>All Expenses</span>
        </a>
        <a href="settings.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
          <span>Settings</span>
        </a>
      </div>
    </nav>
  </div>

  <script type="py" src="./main.py"></script>
</body>
</html>
