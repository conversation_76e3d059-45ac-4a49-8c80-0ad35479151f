import js
from pyodide.ffi import create_proxy
from pyscript import document
import json
import io
import csv
from datetime import datetime
from pyodide.http import pyfetch
import asyncio

# --- Constants ---
EXPENSES_STORAGE_KEY = "pyscript_expense_tracker_data"
USER_CONFIG_KEY = "pyscript_expense_tracker_config"
REGIONAL_PARTNERS_FILE = "support-partners.json"  # Keeping the same filename for backward compatibility

# --- Global State ---
expenses = [] # List to store expense dictionaries (will be loaded from localStorage)
userconfig = {
    'home-currency': "USD",
    'destination-currency': "USD",
    'exchange-rate': "1.00",
    'budgets': {
        'Food': "0.00",
        'Transport': "0.00",
        'Fuel': "0.00",
        'Entertainment': "0.00",
        'Utilities': "0.00",
        'Shopping': "0.00",
        'Groceries': "0.00",
        'Health': "0.00",
        'Other': "0.00",
        'Final': "0.00"
    },
    'running-balance': "0.00"
} # Store currency names, exchange rate, budget amounts, and running balance
support_partners = {} # Dictionary to store support partners data from JSON file
expense_categories = {} # Dictionary to store expense categories loaded from JSON file

# --- DOM Element References ---
export_button = document.getElementById("export-button")
import_button = document.getElementById("import-button")
csv_file_input = document.getElementById("csv-file-input")
status_area = document.getElementById("status_area")
delete_all_button = document.getElementById("delete-all-button")
delete_budget_button = document.getElementById("delete-budget-button")

def show_status(message, is_success=True):
    """Displays a status message to the user."""
    if not status_area:
        print(f"Status Error: Cannot find status area element. Message: {message}")
        js.alert(f"Status: {message}") # Fallback to alert if status area missing
        return

    status_area.innerHTML = ""
    div = document.createElement("div")
    div.textContent = message
    div.classList.add("status-message")
    div.classList.add("success" if is_success else "error")
    status_area.appendChild(div)
    # Clear message after 3 seconds using setTimeout from js module
    clear_proxy = create_proxy(lambda: status_area.removeChild(div) if status_area.contains(div) else None)
    js.setTimeout(clear_proxy, 3000)

def load_expenses():
    """Loads expenses from localStorage."""
    global expenses
    try:
        stored_data = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
        if stored_data:
            data = json.loads(stored_data)
            print(f"Raw data loaded: {len(data)} items")

            # Process and validate each item
            temp_expenses = []
            for i, item in enumerate(data):
                try:
                    # Extract and validate required fields
                    desc_main = item.get('description_main', '')
                    desc_additional = item.get('description_additional', '')
                    description = f"{desc_main} - {desc_additional}" if desc_additional else desc_main

                    # Ensure ID is an integer
                    exp_id = item.get('exp_id')
                    if exp_id is not None:
                        try:
                            exp_id = int(exp_id)
                        except (ValueError, TypeError):
                            print(f"Warning: Invalid ID format in stored data: {exp_id}, using index")
                            exp_id = i + 1
                    else:
                        exp_id = i + 1

                    temp_expenses.append({
                        'id': exp_id, # Use saved ID or generate
                        'desc_main': desc_main,
                        'desc_additional': desc_additional,
                        'description': description,
                        'amount_home': float(item.get('amount_home', item.get('amount', 0.0))), # Backward compatibility
                        'amount_destination': float(item.get('amount_destination', 0.0)),
                        'currency_home': item.get('currency_home', userconfig['home-currency']),  # Load stored currency or use default
                        'currency_destination': item.get('currency_destination', userconfig['destination-currency']),  # Load stored currency or use default
                        'expense_roe': item.get('expense_roe', userconfig['exchange-rate']),  # Load stored exchange rate or use default
                        'date': item['date'],
                        'date_obj': datetime.strptime(item['date'], '%Y-%m-%d').date() if 'date' in item else datetime.now().date(),
                        'category': item.get('category', 'Other'),
                        'category_budget': item.get('category_budget', "0.00"),
                        'payment_method': item.get('payment_method', ''),
                        'location': item.get('location', ''),
                        'tags': item.get('tags', []),
                        'expense_goal': item.get('expense_goal', ''),
                        'support_partner': item.get('support_partner', '')
                    })
                except (ValueError, KeyError, TypeError) as parse_err:
                    print(f"Skipping corrupted item during load: {item}, Error: {parse_err}")
            expenses = temp_expenses # Assign loaded data to global variable
            print(f"Loaded {len(expenses)} expenses.")
        else:
            print("No saved data found in localStorage.")
            expenses = [] # Ensure it's an empty list if nothing is loaded
    except Exception as e:
        print(f"Error loading expenses from localStorage: {e}")
        expenses = []
        show_status("Could not load saved data. Starting fresh.", is_success=False)

def save_expenses():
    """Saves the current expenses list to localStorage."""
    try:
        # Prepare data for saving (ensure consistency)
        expenses_to_save = []
        for i, exp in enumerate(expenses):
            # Ensure ID is an integer before saving
            exp_id = int(exp.get('id', i + 1)) if exp.get('id') is not None else i + 1
            expenses_to_save.append({
                'exp_id': exp_id, # Assign ID if missing, ensure uniqueness might be needed later
                'description_main': exp.get('desc_main', ''),
                'description_additional': exp.get('desc_additional', ''),
                'description': exp.get('description', ''), # Recalculated on load/add if needed
                'amount_home': exp.get('amount_home', exp.get('amount', 0.0)),  # Backward compatibility
                'amount_destination': exp.get('amount_destination', 0.0),
                'currency_home': exp.get('currency_home', userconfig['home-currency']),  # Store the currency
                'currency_destination': exp.get('currency_destination', userconfig['destination-currency']),  # Store the currency
                'expense_roe': exp.get('expense_roe', userconfig['exchange-rate']),  # Store the exchange rate
                'date': exp.get('date', ''),
                'category': exp.get('category', 'Other'),
                'category_budget': exp.get('category_budget', "0.00"),
                'payment_method': exp.get('payment_method', ''),
                'location': exp.get('location', ''),
                'tags': exp.get('tags', []),
                'expense_goal': exp.get('expense_goal', ''),
                'support_partner': exp.get('support_partner', '')
            })

        expenses_json = json.dumps(expenses_to_save)
        js.localStorage.setItem(EXPENSES_STORAGE_KEY, expenses_json)
        print(f"Saved {len(expenses)} expenses to localStorage.")
    except Exception as e:
        print(f"Error saving expenses to localStorage: {e}")
        show_status("Error saving data. Changes might be lost on refresh.", is_success=False)

def load_user_config():
    """Loads user configuration from localStorage."""
    global userconfig
    try:
        stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
        if stored_config:
            config = json.loads(stored_config)
            print("Loaded user configuration:", config)

            # Update the global userconfig
            if 'home-currency' in config:
                userconfig['home-currency'] = config['home-currency']

            if 'destination-currency' in config:
                userconfig['destination-currency'] = config['destination-currency']

            if 'exchange-rate' in config:
                userconfig['exchange-rate'] = config['exchange-rate']
            else:
                userconfig['exchange-rate'] = "1.00"  # Default value

            # Load budget amounts
            if 'budgets' in config:
                userconfig['budgets'] = config['budgets']

            # Load running balance if available
            if 'running-balance' in config:
                userconfig['running-balance'] = config['running-balance']
            else:
                userconfig['running-balance'] = "0.00"
        else:
            print("No saved configuration found. Using defaults.")
    except Exception as e:
        print(f"Error loading configuration: {e}")
        show_status("Error loading configuration. Using defaults.", is_success=False)

def import_csv_data(csv_text):
    """Imports expense data from CSV text."""
    try:
        # Remove UTF-8 BOM if present
        if csv_text.startswith('\ufeff'):
            csv_text = csv_text[1:]
            print("Removed UTF-8 BOM from CSV data")

        # Create a StringIO object from the CSV text
        csv_buffer = io.StringIO(csv_text)

        # Create a CSV reader
        reader = csv.reader(csv_buffer)

        # Read the header row
        header = next(reader)
        print(f"CSV Header: {header}")

        # Print the raw CSV data for debugging
        print(f"Raw CSV data (first 200 chars): {csv_text[:200]}")

        # Map header indices
        header_map = {}
        expected_headers = [
            'ID', 'Date', 'Primary Description', 'Additional Description',
            'Home Currency Amount', 'Home Currency',
            'Destination Currency Amount', 'Destination Currency',
            'Exchange Rate',
            'Category', 'Category Budget', 'Payment Method', 'Location', 'Tags',
            'Expense Goal', 'Regional Partner', 'Support Partner'  # Include both for backward compatibility
        ]

        for i, column in enumerate(header):
            # Clean up column name and check for matches
            clean_column = column.strip()

            # Check for BOM in the first column
            if i == 0 and clean_column.startswith('\ufeff'):
                clean_column = clean_column[1:]
                print(f"Removed BOM from first column name: {clean_column}")

            # Special case for ID column with BOM
            if clean_column == '\ufeffID' or clean_column == 'ï»¿ID':
                clean_column = 'ID'
                print("Fixed ID column with BOM")

            if clean_column in expected_headers:
                header_map[clean_column] = i
            else:
                print(f"Warning: Unexpected column '{clean_column}' at index {i}")

        # Check for required columns
        required_columns = ['Date', 'Primary Description', 'Category']
        for col in required_columns:
            if col not in header_map:
                return [], f"Error: Required column '{col}' not found in CSV."

        # Parse the data rows
        imported_expenses = []
        for row_num, row in enumerate(reader, start=2):  # Start at 2 to account for header row
            try:
                # Skip empty rows
                if not row or all(cell.strip() == '' for cell in row):
                    continue

                # Extract data using the header map
                expense_data = {}

                # Get date
                date_str = row[header_map['Date']].strip()
                try:
                    # Try different date formats
                    try:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                    except ValueError:
                        # Try alternative format with +AC0- encoding
                        date_str = date_str.replace('+AC0-', '-')
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                except ValueError:
                    print(f"Warning: Invalid date format in row {row_num}: {date_str}")
                    continue

                expense_data['date'] = date_str
                expense_data['date_obj'] = date_obj

                # Get descriptions
                expense_data['desc_main'] = row[header_map['Primary Description']].strip()

                # Check for Additional Description first, then fall back to Secondary Description for backward compatibility
                if 'Additional Description' in header_map and header_map.get('Additional Description', -1) < len(row):
                    expense_data['desc_additional'] = row[header_map.get('Additional Description', -1)].strip()
                elif 'Secondary Description' in header_map and header_map.get('Secondary Description', -1) < len(row):
                    expense_data['desc_additional'] = row[header_map.get('Secondary Description', -1)].strip()
                else:
                    expense_data['desc_additional'] = ''

                expense_data['description'] = f"{expense_data['desc_main']} - {expense_data['desc_additional']}" if expense_data['desc_additional'] else expense_data['desc_main']

                # Get amounts and currencies
                amount_home_str = row[header_map.get('Home Currency Amount', -1)].strip() if 'Home Currency Amount' in header_map and header_map.get('Home Currency Amount', -1) < len(row) else '0.00'
                amount_destination_str = row[header_map.get('Destination Currency Amount', -1)].strip() if 'Destination Currency Amount' in header_map and header_map.get('Destination Currency Amount', -1) < len(row) else '0.00'

                # Convert amounts to float, handling empty strings
                try:
                    expense_data['amount_home'] = float(amount_home_str) if amount_home_str else 0.0
                except ValueError:
                    print(f"Warning: Invalid home amount in row {row_num}: {amount_home_str}")
                    expense_data['amount_home'] = 0.0

                try:
                    expense_data['amount_destination'] = float(amount_destination_str) if amount_destination_str else 0.0
                except ValueError:
                    print(f"Warning: Invalid destination amount in row {row_num}: {amount_destination_str}")
                    expense_data['amount_destination'] = 0.0

                # Get currencies
                expense_data['currency_home'] = row[header_map.get('Home Currency', -1)].strip() if 'Home Currency' in header_map and header_map.get('Home Currency', -1) < len(row) else userconfig['home-currency']
                expense_data['currency_destination'] = row[header_map.get('Destination Currency', -1)].strip() if 'Destination Currency' in header_map and header_map.get('Destination Currency', -1) < len(row) else userconfig['destination-currency']

                # Get exchange rate
                expense_roe_str = row[header_map.get('Exchange Rate', -1)].strip() if 'Exchange Rate' in header_map and header_map.get('Exchange Rate', -1) < len(row) else userconfig['exchange-rate']
                try:
                    expense_data['expense_roe'] = expense_roe_str
                except ValueError:
                    print(f"Warning: Invalid exchange rate in row {row_num}: {expense_roe_str}")
                    expense_data['expense_roe'] = userconfig['exchange-rate']

                # Get category and budget
                expense_data['category'] = row[header_map['Category']].strip()

                # Get category budget
                category_budget = row[header_map.get('Category Budget', -1)].strip() if 'Category Budget' in header_map and header_map.get('Category Budget', -1) < len(row) else "0.00"
                try:
                    # Validate and format category budget
                    budget_value = float(category_budget) if category_budget else 0.0
                    expense_data['category_budget'] = f"{budget_value:.2f}"
                except ValueError:
                    print(f"Warning: Invalid category budget in row {row_num}: {category_budget}")
                    expense_data['category_budget'] = "0.00"

                # Get other fields
                expense_data['payment_method'] = row[header_map.get('Payment Method', -1)].strip() if 'Payment Method' in header_map and header_map.get('Payment Method', -1) < len(row) else ''
                expense_data['location'] = row[header_map.get('Location', -1)].strip() if 'Location' in header_map and header_map.get('Location', -1) < len(row) else ''

                # Get tags
                tags_str = row[header_map.get('Tags', -1)].strip() if 'Tags' in header_map and header_map.get('Tags', -1) < len(row) else ''
                expense_data['tags'] = [tag.strip() for tag in tags_str.split(',')] if tags_str else []

                # Get expense goal and regional partner
                expense_data['expense_goal'] = row[header_map.get('Expense Goal', -1)].strip() if 'Expense Goal' in header_map and header_map.get('Expense Goal', -1) < len(row) else ''

                # Check for Regional Partner first, then fall back to Support Partner for backward compatibility
                if 'Regional Partner' in header_map and header_map.get('Regional Partner', -1) < len(row):
                    expense_data['support_partner'] = row[header_map.get('Regional Partner', -1)].strip()
                elif 'Support Partner' in header_map and header_map.get('Support Partner', -1) < len(row):
                    expense_data['support_partner'] = row[header_map.get('Support Partner', -1)].strip()
                else:
                    expense_data['support_partner'] = ''

                # Add ID if available
                if 'ID' in header_map and header_map['ID'] < len(row) and row[header_map['ID']].strip():
                    try:
                        expense_data['id'] = int(row[header_map['ID']])
                        print(f"  Using ID from CSV: {expense_data['id']}")
                    except ValueError:
                        print(f"  Invalid ID in CSV: '{row[header_map['ID']]}', using default")
                        # Don't pass silently - assign a default ID that will be replaced later

                imported_expenses.append(expense_data)

            except Exception as row_error:
                print(f"Error processing row {row_num}: {row_error}")
                continue

        return imported_expenses, f"Successfully imported {len(imported_expenses)} expenses."

    except Exception as e:
        print(f"Error importing CSV: {e}")
        return [], f"Error importing CSV: {e}"

def export_data_handler(_):
    """Exports the current expenses list to a CSV file and triggers download."""
    if not expenses:
        show_status("No expenses to export.", is_success=False)
        return

    # Use a buffer that handles Unicode correctly
    output = io.StringIO()
    # Define fieldnames based on the simplified CSV format
    fieldnames = [
        'ID', 'Date', 'Primary Description', 'Additional Description',
        'Home Currency Amount', 'Home Currency',
        'Destination Currency Amount', 'Destination Currency',
        'Exchange Rate',
        'Category', 'Category Budget', 'Payment Method', 'Location', 'Tags',
        'Expense Goal', 'Regional Partner'
    ]
    # Use standard csv.writer with explicit line endings and dialect
    writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL, lineterminator='\r\n')
    writer.writerow(fieldnames) # Write header row

    # Sort by date before exporting (oldest first for CSV export)
    # Keep using date for CSV export as it makes more sense for external tools
    # Ensure ID is treated as an integer for consistent sorting
    sorted_expenses_export = sorted(expenses, key=lambda x: (x.get('date_obj', datetime.min.date()), int(x.get('id', 0))), reverse=False)

    for expense in sorted_expenses_export:
        tags_str = ','.join(expense.get('tags', [])) if isinstance(expense.get('tags'), list) else expense.get('tags', '')
        # Format amounts as strings with 2 decimal places
        amount_home = f"{expense.get('amount_home', expense.get('amount', 0.0)):.2f}"
        amount_destination = f"{expense.get('amount_destination', 0.0):.2f}"

        # Replace 0.00 with empty string for better readability
        if amount_home == "0.00": amount_home = ""
        if amount_destination == "0.00": amount_destination = ""

        # Ensure ID is an integer before writing to CSV
        expense_id = expense.get('id', '')
        if expense_id != '':
            try:
                expense_id = int(expense_id)
            except (ValueError, TypeError):
                print(f"Warning: Invalid ID format when exporting: {expense_id}")

        row_data = [
            expense_id,  # Include the expense ID as an integer
            expense.get('date', ''),
            expense.get('desc_main', ''),
            expense.get('desc_additional', ''),
            amount_home,
            expense.get('currency_home', userconfig['home-currency']),
            amount_destination,
            expense.get('currency_destination', userconfig['destination-currency']),
            expense.get('expense_roe', userconfig['exchange-rate']),
            expense.get('category', ''),
            expense.get('category_budget', "0.00"),
            expense.get('payment_method', ''),
            expense.get('location', ''),
            tags_str,
            expense.get('expense_goal', ''),
            expense.get('support_partner', '')  # Using the field name 'Regional Partner' in the CSV header but 'support_partner' internally
        ]
        writer.writerow(row_data)

    csv_data = output.getvalue()
    output.close()

    # Debug: Print a sample of the CSV data
    print(f"CSV Sample (first 200 chars): {csv_data[:200]}")

    try:
        # Use text/csv MIME type with UTF-8 encoding
        # Removed UTF-8 BOM as it causes issues with import
        blob = js.Blob.new([csv_data], {type: "text/csv;charset=utf-8;"})
        url = js.URL.createObjectURL(blob)
        link = document.createElement("a")
        link.href = url
        # Use a timestamp in the filename
        filename = f"expenses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        link.download = filename
        document.body.appendChild(link) # Append link to body to ensure click works in all browsers
        link.click() # Simulate click to trigger download
        document.body.removeChild(link) # Clean up the link
        js.URL.revokeObjectURL(url) # Release the object URL
        show_status(f"Data exported to {filename}", is_success=True)
    except Exception as e:
        print(f"Error during CSV export: {e}")
        show_status(f"Could not export CSV: {e}", is_success=False)

def handle_file_import(event):
    """Handles the file import process."""
    global expenses

    # Get the selected file
    try:
        # Access files using JavaScript property access
        files = event.target.files
        file = files.item(0) if files.length > 0 else None
        print(f"Selected file: {file.name if file else 'None'}")
    except Exception as e:
        print(f"Error accessing file: {e}")
        show_status(f"Error accessing file: {e}", is_success=False)
        return

    if not file:
        show_status("No file selected.", is_success=False)
        return

    # Check file type
    if not file.name.endswith('.csv'):
        show_status("Please select a CSV file.", is_success=False)
        return

    # Create a FileReader to read the file
    reader = js.FileReader.new()

    # Define what happens when the file is loaded
    def on_load(e):
        try:
            # Get the file content as text
            csv_text = e.target.result

            # Remove UTF-8 BOM if present
            if csv_text.startswith('\ufeff'):
                csv_text = csv_text[1:]
                print("Removed UTF-8 BOM from imported file")

            print(f"File content (first 200 chars): {csv_text[:200]}")

            # Import the CSV data
            imported_expenses, message = import_csv_data(csv_text)

            if not imported_expenses:
                show_status(message, is_success=False)
                return

            # Ask for confirmation before importing
            if js.confirm(f"{message}\n\nDo you want to add these expenses to your existing data?"):
                # Generate new IDs for imported expenses to avoid conflicts
                max_id = max([int(exp.get('id', 0)) for exp in expenses] + [0])
                for i, exp in enumerate(imported_expenses):
                    if 'id' not in exp:
                        exp['id'] = max_id + i + 1
                    else:
                        # Ensure ID is an integer
                        try:
                            exp['id'] = int(exp['id'])
                        except (ValueError, TypeError):
                            print(f"Warning: Invalid ID in imported expense: {exp['id']}, assigning new ID")
                            exp['id'] = max_id + i + 1

                # Add the imported expenses to the existing expenses
                expenses.extend(imported_expenses)

                # Save the updated expenses to localStorage
                save_expenses()

                show_status(f"Successfully imported {len(imported_expenses)} expenses.", is_success=True)
            else:
                show_status("Import cancelled.", is_success=False)

        except Exception as e:
            print(f"Error handling file import: {e}")
            show_status(f"Error importing file: {e}", is_success=False)

    # Set up the FileReader event handlers
    on_load_proxy = create_proxy(on_load)
    reader.onload = on_load_proxy

    # Read the file as text with UTF-8 encoding
    reader.readAsText(file, 'UTF-8')

async def load_support_partners():
    """Loads the regional partners data from the JSON file."""
    global support_partners
    try:
        response = await pyfetch(REGIONAL_PARTNERS_FILE)
        if response.status == 200:
            support_partners = await response.json()
            print("Loaded regional partners:", support_partners)
            return True
        else:
            print(f"Error loading regional partners: HTTP {response.status}")
            return False
    except Exception as e:
        print(f"Error loading regional partners: {e}")
        return False

async def load_expense_categories():
    """Loads the expense categories data from the JSON file."""
    global expense_categories
    try:
        response = await pyfetch("expense_category_list.json")
        if response.status == 200:
            expense_categories = await response.json()
            print("Loaded expense categories:", list(expense_categories.keys()))
            return True
        else:
            print(f"Error loading expense categories: HTTP {response.status}")
            return False
    except Exception as e:
        print(f"Error loading expense categories: {e}")
        return False

async def initial_setup():
    """Loads data and sets up event listeners."""
    print("Running initial setup for data management page...")

    # Load support partners data first
    await load_support_partners()

    # Load expense categories
    await load_expense_categories()

    # Load user configuration
    load_user_config()

    # Load expenses
    load_expenses()

    # Set up event listeners
    if export_button:
        export_proxy = create_proxy(export_data_handler)
        export_button.addEventListener("click", export_proxy)
    else:
        print("Warning: Export button not found.")

    if import_button and csv_file_input:
        import_button_click_proxy = create_proxy(lambda _event: csv_file_input.click())
        import_button.addEventListener("click", import_button_click_proxy)

        file_input_change_proxy = create_proxy(handle_file_import)
        csv_file_input.addEventListener("change", file_input_change_proxy)
    else:
        print("Warning: Import button or file input not found.")

    # Add event listeners for delete buttons
    if delete_all_button:
        delete_all_proxy = create_proxy(delete_all_expenses)
        delete_all_button.addEventListener("click", delete_all_proxy)
    else:
        print("Warning: Delete All Expenses button not found.")

    if delete_budget_button:
        delete_budget_proxy = create_proxy(delete_budget_config)
        delete_budget_button.addEventListener("click", delete_budget_proxy)
    else:
        print("Warning: Delete Budget Data button not found.")

def delete_all_expenses(_event):
    """Deletes all expense records from localStorage after confirmation."""
    if js.confirm("Confirm deletion of all expense records"):
        # global expenses # Only needed if you want to manipulate an in-memory list too
        try:
            # expenses = [] # Optional: if you maintain an in-memory list on this page
            js.localStorage.removeItem(EXPENSES_STORAGE_KEY)
            print("All expenses deleted from localStorage.")
            show_status("All expenses deleted successfully.", is_success=True)
            #render_expense_cards() # REMOVED: This is likely not needed/wanted on userconfig.html
        except Exception as e:
            print(f"Error deleting all expenses: {e}")
            show_status(f"Error deleting all expenses: {e}", is_success=False)

def delete_budget_config(_event):
    """Deletes budget configuration, currency and exchange rate values from localStorage after confirmation."""
    if js.confirm("Confirm deletion of Budget configuration"):
        global userconfig
        try:
            # Reset userconfig to defaults
            userconfig = {
                'home-currency': "USD",
                'destination-currency': "USD",
                'exchange-rate': "1.00",
                'budgets': {
                    'Food': "0.00",
                    'Transport': "0.00",
                    'Fuel': "0.00",
                    'Entertainment': "0.00",
                    'Utilities': "0.00",
                    'Shopping': "0.00",
                    'Groceries': "0.00",
                    'Health': "0.00",
                    'Other': "0.00",
                    'Final': "0.00"
                },
                'running-balance': "0.00"
            }
            # Remove from localStorage
            js.localStorage.removeItem(USER_CONFIG_KEY)
            print("Budget configuration deleted from localStorage.")
            show_status("Budget configuration deleted successfully.", is_success=True)
        except Exception as e:
            print(f"Error deleting budget configuration: {e}")
            show_status(f"Error deleting budget configuration: {e}", is_success=False)

# Removed setup_userconfig_buttons function and its call to prevent duplicate event listeners
# The event listeners for delete buttons are already set up in the initial_setup function

# Run initial setup
asyncio.ensure_future(initial_setup())
