import json
import js
from pyodide.http import pyfetch
from pyscript import document, display

# --- Configuration ---
USERCONFIG_STORAGE_KEY = 'userconfig'
EXPENSES_STORAGE_KEY = 'expenses_data' # Make sure this matches your actual key
CATEGORIES_JSON_PATH = './expense_categories.json' # Path to your categories JSON file

# --- Helper Functions ---

async def load_user_config():
    """Loads user configuration from localStorage."""
    try:
        config_str = js.localStorage.getItem(USERCONFIG_STORAGE_KEY)
        if config_str:
            return json.loads(config_str)
        return {} # Return empty dict if not found
    except Exception as e:
        print(f"Error loading user config: {e}")
        return {}

async def load_expense_categories_from_file():
    """Loads expense categories data from the JSON file."""
    try:
        response = await pyfetch(CATEGORIES_JSON_PATH)
        if response.status == 200:
            categories_json = await response.json()
            return categories_json
        else:
            print(f"Error fetching categories JSON: Status {response.status}")
            return {}
    except Exception as e:
        print(f"Error loading expense categories: {e}")
        return {}

def load_stored_expenses():
    """Loads stored expense items from localStorage."""
    try:
        stored_data_str = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
        if stored_data_str:
            return json.loads(stored_data_str)
        return [] # Return empty list if no expenses
    except Exception as e:
        print(f"Error loading stored expenses: {e}")
        return []

def get_expense_currency(expense_item, app_home_currency):
    """Determines the currency for a given expense item."""
    # The expense item's own 'home_currency_code' field reflects the app's home currency at time of saving.
    # The 'destination_currency_code' field is the actual currency of the transaction if it was different.
    item_home_curr = expense_item.get('home_currency_code', app_home_currency)
    item_dest_curr = expense_item.get('destination_currency_code')

    if item_dest_curr and item_dest_curr != item_home_curr:
        return item_dest_curr
    return item_home_curr


# --- Main Report Generation Logic ---
async def generate_report():
    """Generates and displays the expense report."""
    print("Generating report...")

    # 1. Load data
    user_config = await load_user_config()
    all_categories_data = await load_expense_categories_from_file() # This is the master list of cat/sub-cat
    stored_expenses = load_stored_expenses()

    app_home_currency = user_config.get('home-currency', 'N/A')
    document.getElementById('home-currency-config').innerText = app_home_currency

    if not stored_expenses:
        display("No expenses found to generate a report.", target="report-container")
        document.getElementById('currencies-count').innerText = "0"
        document.getElementById('categories-count').innerText = "0"
        return

    # 2. Process expenses and aggregate data
    # Structure: { currency: { category: { sub_category: total, '_total': category_total } } }
    report_data = {}
    # Structure: { currency: total }
    grand_totals_by_currency = {}
    
    active_currencies = set()
    active_categories = set()

    for expense in stored_expenses:
        try:
            amount = float(expense.get('amount', 0))
            if amount == 0:
                continue

            category = expense.get('category')
            sub_category = expense.get('sub_category')
            
            # Determine the currency for this specific expense
            # The user_config['home-currency'] is the app's default.
            # An expense item might have its own home_currency_code and destination_currency_code.
            # If destination_currency_code is different from home_currency_code, that's the currency of the expense.
            # Otherwise, it's the home_currency_code of the expense.
            
            exp_home_curr = expense.get('home_currency_code', app_home_currency)
            exp_dest_curr = expense.get('destination_currency_code', exp_home_curr) # Default to exp_home_curr if not present

            actual_currency = exp_dest_curr if exp_dest_curr != exp_home_curr else exp_home_curr
            
            if not category or not sub_category or not actual_currency:
                print(f"Skipping expense due to missing data: {expense}")
                continue

            active_currencies.add(actual_currency)
            active_categories.add(category)

            # Initialize currency in report_data if not exists
            report_data.setdefault(actual_currency, {})
            # Initialize category in currency if not exists
            report_data[actual_currency].setdefault(category, {'_total': 0.0})
            # Initialize sub_category in category if not exists
            report_data[actual_currency][category].setdefault(sub_category, 0.0)

            # Add amounts
            report_data[actual_currency][category][sub_category] += amount
            report_data[actual_currency][category]['_total'] += amount
            
            # Add to grand totals
            grand_totals_by_currency.setdefault(actual_currency, 0.0)
            grand_totals_by_currency[actual_currency] += amount

        except ValueError:
            print(f"Skipping expense due to invalid amount: {expense}")
        except Exception as e:
            print(f"Error processing expense {expense.get('id', 'N/A')}: {e}")

    # Update summary info
    document.getElementById('currencies-count').innerText = str(len(active_currencies))
    document.getElementById('currencies-list').innerText = ", ".join(sorted(list(active_currencies))) if active_currencies else "None"
    document.getElementById('categories-count').innerText = str(len(active_categories))
    document.getElementById('categories-list').innerText = ", ".join(sorted(list(active_categories))) if active_categories else "None"


    # 3. Render cards
    report_container = document.getElementById('report-container')
    report_container.innerHTML = "" # Clear previous content

    # Sort currencies for consistent report output
    sorted_currencies = sorted(report_data.keys())

    for currency_code in sorted_currencies:
        currency_expenses = report_data[currency_code]
        # Sort categories for consistent report output
        sorted_categories_for_currency = sorted(currency_expenses.keys())

        for category_name in sorted_categories_for_currency:
            category_data = currency_expenses[category_name]
            
            card_html = f"""
            <div class="card">
                <h3>{category_name} ({currency_code})</h3>
            """
            
            # Get subcategories for this main category from the master list to maintain order (optional)
            # master_sub_cats = all_categories_data.get(category_name, [])
            # For simplicity, we'll iterate through the subcategories present in category_data
            # Filter out '_total' and sort sub-categories for consistent display
            
            sub_category_items = []
            for sub_cat, sub_total in category_data.items():
                if sub_cat != '_total' and sub_total > 0:
                    sub_category_items.append((sub_cat, sub_total))
            
            # Sort sub-categories alphabetically for consistent display
            sub_category_items.sort(key=lambda x: x[0])

            if not sub_category_items: # No expenses in sub-categories for this category/currency combo
                card_html += "<p>No expenses recorded for these sub-categories.</p>"
            else:
                for sub_cat, sub_total in sub_category_items:
                    card_html += f"""
                        <p>
                            <span class="label">{sub_cat}:</span>
                            <span class="value">{sub_total:.2f} {currency_code}</span>
                        </p>
                    """
            
            card_html += f"""
                <p class="category-total">
                    <span class="label">Total for {category_name}:</span>
                    <span class="value">{category_data['_total']:.2f} {currency_code}</span>
                </p>
            </div>
            """
            report_container.innerHTML += card_html

    # 4. Render Grand Totals
    grand_totals_container = document.getElementById('grand-totals-container')
    grand_totals_container.innerHTML = "<h2>Grand Totals</h2>" # Reset header

    if not grand_totals_by_currency:
        grand_totals_container.innerHTML += "<p>No expenses to total.</p>"
    else:
        for currency_code, total_amount in sorted(grand_totals_by_currency.items()):
            grand_totals_container.innerHTML += f"<p>Total in {currency_code}: <strong>{total_amount:.2f}</strong></p>"
    
    print("Report generation complete.")

# --- Pyscript Entry Point ---
async def main():
    await generate_report()

# Run the main function when the script loads
# Pyscript runs top-level await automatically, or you can use a specific trigger.
# For simplicity, let's assume it runs. If you need an explicit trigger, use events.
# Example for explicit trigger (e.g., a button):
# py_button = document.createElement("button")
# py_button.innerText = "Generate Report"
# py_button.onclick = main # Pass the async function directly
# document.body.appendChild(py_button) # or add to a specific element

# Directly call main for auto-execution on load by Pyscript
await main()