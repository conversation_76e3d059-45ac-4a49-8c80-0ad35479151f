import json
import js
from pyodide.http import pyfetch
from pyscript import document

# --- Constants ---
EXPENSES_STORAGE_KEY = "pyscript_expense_tracker_data"
USER_CONFIG_KEY = "pyscript_expense_tracker_config"
EXPENSE_CATEGORIES_FILE = "expense_category_list.json"

# --- Global State ---
expenses = []
userconfig = {
    'home-currency': "USD",
    'destination-currency': "USD",
    'exchange-rate': "1.00",
    'budgets': {
        'Accommodation': "0.00",
        'Education': "0.00",
        'Entertainment': "0.00",
        'Financial': "0.00",
        'Food': "0.00",
        'Gifts': "0.00",
        'Health': "0.00",
        'Home': "0.00",
        'Kids': "0.00",
        'PersonalCare': "0.00",
        'Pets': "0.00",
        'Shopping': "0.00",
        'Transport': "0.00",
        'Travel': "0.00",
        'Utilities': "0.00",
        'Work': "0.00",
        'Other': "0.00"
    },
    'running-balance': "0.00"
}
expense_categories = {}

# --- Helper Functions ---

def get_category_group(category):
    """Extract the category group from a category string (e.g., 'Food-Groceries' -> 'Food')."""
    if '-' in category:
        return category.split('-')[0]
    return category

async def load_expense_categories():
    """Load expense categories from JSON file."""
    global expense_categories
    try:
        response = await pyfetch(EXPENSE_CATEGORIES_FILE)
        if response.status == 200:
            expense_categories = await response.json()
            print(f"Loaded {len(expense_categories)} expense category groups")
        else:
            print(f"Error loading expense categories: Status {response.status}")
    except Exception as e:
        print(f"Error loading expense categories: {e}")

def load_user_config():
    """Load user configuration from localStorage."""
    global userconfig
    try:
        stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
        if stored_config:
            config = json.loads(stored_config)

            # Update userconfig with stored values
            userconfig['home-currency'] = config.get('home-currency', 'USD')
            userconfig['destination-currency'] = config.get('destination-currency', 'USD')
            userconfig['exchange-rate'] = config.get('exchange-rate', '1.00')

            # Load budget amounts if available
            if 'budgets' in config:
                userconfig['budgets'] = config['budgets']

            # Load running balance if available
            if 'running-balance' in config:
                userconfig['running-balance'] = config['running-balance']
            else:
                userconfig['running-balance'] = "0.00"

            print(f"Loaded user config: Home={userconfig['home-currency']}, Dest={userconfig['destination-currency']}")
        else:
            print("No user config found, using defaults")
    except Exception as e:
        print(f"Error loading user config: {e}")

def load_expenses():
    """Load expenses from localStorage."""
    global expenses
    try:
        stored_data = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
        if stored_data:
            print("Found saved data in localStorage. Loading...")
            loaded_list = json.loads(stored_data)
            temp_expenses = []
            for i, item in enumerate(loaded_list):
                try:
                    # Handle both new and old variable names for backward compatibility
                    desc_main = item.get('description_main', item.get('description_primary', ''))
                    desc_additional = item.get('description_additional', item.get('description_secondary', ''))

                    # Create expense object with consistent field names
                    expense_obj = {
                        'id': item.get('exp_id', i + 1),
                        'desc_main': desc_main,
                        'desc_additional': desc_additional,
                        'description': f"{desc_main} - {desc_additional}" if desc_additional else desc_main,
                        'amount_home': float(item.get('amount_home', item.get('amount', 0.0))),
                        'amount_destination': float(item.get('amount_destination', 0.0)),
                        'date': item.get('date', ''),
                        'currency_home': item.get('currency_home', userconfig['home-currency']),
                        'currency_destination': item.get('currency_destination', userconfig['destination-currency']),
                        'expense_roe': item.get('expense_roe', userconfig['exchange-rate']),
                        'category': item.get('category', 'Other'),
                        'category_budget': item.get('category_budget', "0.00"),
                        'payment_method': item.get('payment_method', ''),
                        'location': item.get('location', ''),
                        'tags': item.get('tags', []),
                        'expense_goal': item.get('expense_goal', ''),
                        'support_partner': item.get('support_partner', '')
                    }

                    temp_expenses.append(expense_obj)
                except Exception as e:
                    print(f"Error processing expense item {i}: {e}")
                    continue

            expenses = temp_expenses
            print(f"Loaded {len(expenses)} expenses from localStorage")
        else:
            print("No expenses found in localStorage")
            expenses = []
    except Exception as e:
        print(f"Error loading expenses: {e}")
        expenses = []


def determine_expense_currency(expense):
    """Determine which currency to use for an expense based on amounts."""
    amount_home = expense.get('amount_home', 0.0)
    amount_destination = expense.get('amount_destination', 0.0)
    currency_home = expense.get('currency_home', userconfig['home-currency'])
    currency_destination = expense.get('currency_destination', userconfig['destination-currency'])

    # If destination amount exists and is greater than 0, use destination currency
    if amount_destination > 0:
        return currency_destination, amount_destination
    else:
        return currency_home, amount_home

def show_status(message, is_success=True):
    """Display status message."""
    status_area = document.getElementById("status_area")
    if status_area:
        status_class = "success" if is_success else "error"
        status_area.innerHTML = f'<div class="status-message {status_class}">{message}</div>'

def test_data_availability():
    """Test function to check if data is available."""
    print("=== DATA AVAILABILITY TEST ===")

    # Check localStorage directly
    stored_data = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
    if stored_data:
        try:
            data = json.loads(stored_data)
            print(f"Found {len(data)} items in localStorage")
            if len(data) > 0:
                print(f"First item: {data[0]}")
        except Exception as e:
            print(f"Error parsing localStorage data: {e}")
    else:
        print("No data found in localStorage")

    # Check userconfig
    stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
    if stored_config:
        try:
            config = json.loads(stored_config)
            print(f"Found userconfig: {config}")
        except Exception as e:
            print(f"Error parsing userconfig: {e}")
    else:
        print("No userconfig found in localStorage")

    print("=== END TEST ===")
    return stored_data is not None

# --- Main Report Generation Logic ---
async def generate_report():
    """Generates and displays the expense report."""
    print("Generating expense report...")

    # Test data availability first
    test_data_availability()

    # Load data
    await load_expense_categories()
    load_user_config()
    load_expenses()

    # Update home currency display
    home_currency_element = document.getElementById('home-currency-config')
    if home_currency_element:
        home_currency_element.textContent = userconfig['home-currency']

    if not expenses:
        show_status("No expenses found to generate a report.", False)
        document.getElementById('currencies-count').textContent = "0"
        document.getElementById('categories-count').textContent = "0"
        document.getElementById('currencies-list').textContent = "None"
        document.getElementById('categories-list').textContent = "None"
        return

    print(f"Processing {len(expenses)} expenses for report generation...")
    # Debug: Print first few expenses to see their structure
    for i, exp in enumerate(expenses[:3]):
        print(f"Expense {i+1}: category='{exp.get('category')}', amount_home={exp.get('amount_home')}, amount_dest={exp.get('amount_destination')}")
        print(f"  currency_home='{exp.get('currency_home')}', currency_dest='{exp.get('currency_destination')}'")
        print(f"  Full expense: {exp}")
        print("---")

    # Process expenses and aggregate data
    # Structure: { currency: { category_group: { subcategory: total, '_total': category_total } } }
    report_data = {}
    grand_totals_by_currency = {}
    active_currencies = set()
    active_category_groups = set()

    for expense in expenses:
        try:
            # Determine currency and amount for this expense
            currency, amount = determine_expense_currency(expense)

            print(f"Processing expense ID {expense.get('id')}: currency={currency}, amount={amount}")

            if amount <= 0:
                print(f"  Skipping expense with zero amount")
                continue

            category = expense.get('category', 'Other')
            category_group = get_category_group(category)

            # Extract subcategory from full category (e.g., 'Food-Groceries' -> 'Groceries')
            if '-' in category:
                subcategory = category.split('-', 1)[1]
            else:
                subcategory = 'General'

            print(f"  category='{category}', group='{category_group}', subcategory='{subcategory}'")

            active_currencies.add(currency)
            active_category_groups.add(category_group)

            # Initialize data structures
            if currency not in report_data:
                report_data[currency] = {}
            if category_group not in report_data[currency]:
                report_data[currency][category_group] = {'_total': 0.0}
            if subcategory not in report_data[currency][category_group]:
                report_data[currency][category_group][subcategory] = 0.0

            # Add amounts
            report_data[currency][category_group][subcategory] += amount
            report_data[currency][category_group]['_total'] += amount

            # Add to grand totals
            if currency not in grand_totals_by_currency:
                grand_totals_by_currency[currency] = 0.0
            grand_totals_by_currency[currency] += amount

            print(f"  Added {amount} to {category_group}/{subcategory} in {currency}")

        except Exception as e:
            print(f"Error processing expense {expense.get('id', 'N/A')}: {e}")
            continue

    print(f"Final report_data structure: {report_data}")
    print(f"Active currencies: {active_currencies}")
    print(f"Active category groups: {active_category_groups}")

    # Update summary info
    currencies_count_element = document.getElementById('currencies-count')
    currencies_list_element = document.getElementById('currencies-list')
    categories_count_element = document.getElementById('categories-count')
    categories_list_element = document.getElementById('categories-list')

    if currencies_count_element:
        currencies_count_element.textContent = str(len(active_currencies))
    if currencies_list_element:
        currencies_list_element.textContent = ", ".join(sorted(active_currencies)) if active_currencies else "None"
    if categories_count_element:
        categories_count_element.textContent = str(len(active_category_groups))
    if categories_list_element:
        categories_list_element.textContent = ", ".join(sorted(active_category_groups)) if active_category_groups else "None"

    # Render report cards
    render_report_cards(report_data)

    # Render grand totals
    render_grand_totals(grand_totals_by_currency)

    show_status(f"Report generated successfully! Found {len(expenses)} expenses across {len(active_currencies)} currencies.", True)
    print("Report generation complete.")

def render_report_cards(report_data):
    """Render the expense report cards."""
    print(f"render_report_cards called with data: {report_data}")

    report_container = document.getElementById('report-container')
    if not report_container:
        print("Error: report-container not found")
        return

    print(f"Found report-container element")
    report_container.innerHTML = ""  # Clear previous content

    if not report_data:
        print("No report data to render")
        report_container.innerHTML = '<div class="report-card"><h3>No Data Available</h3><p>No expenses found to generate report cards.</p></div>'
        return

    # Sort currencies for consistent output
    sorted_currencies = sorted(report_data.keys())
    print(f"Rendering cards for currencies: {sorted_currencies}")

    cards_created = 0
    for currency in sorted_currencies:
        currency_data = report_data[currency]
        # Sort category groups for consistent output
        sorted_category_groups = sorted(currency_data.keys())
        print(f"  Currency {currency} has category groups: {sorted_category_groups}")

        for category_group in sorted_category_groups:
            category_data = currency_data[category_group]
            print(f"    Creating card for {category_group} ({currency})")

            # Create card HTML
            card_html = f'''
            <div class="report-card">
                <h3>{category_group} ({currency})</h3>
            '''

            # Get subcategories (excluding '_total')
            subcategory_items = []
            for subcat, amount in category_data.items():
                if subcat != '_total' and amount > 0:
                    subcategory_items.append((subcat, amount))

            print(f"      Subcategories: {subcategory_items}")

            # Sort subcategories alphabetically
            subcategory_items.sort(key=lambda x: x[0])

            if not subcategory_items:
                card_html += '<div class="subcategory-item"><span class="subcategory-label">No expenses recorded</span><span class="subcategory-value">-</span></div>'
            else:
                for subcat, amount in subcategory_items:
                    card_html += f'''
                    <div class="subcategory-item">
                        <span class="subcategory-label">{subcat}</span>
                        <span class="subcategory-value">{amount:.2f} {currency}</span>
                    </div>
                    '''

            # Add category total
            total_amount = category_data.get('_total', 0.0)
            card_html += f'''
                <div class="category-total">
                    <span>Total for {category_group}</span>
                    <span>{total_amount:.2f} {currency}</span>
                </div>
            </div>
            '''

            report_container.innerHTML += card_html
            cards_created += 1
            print(f"      Card HTML added for {category_group} ({currency})")

    print(f"Total cards created: {cards_created}")
    print(f"Final report container HTML length: {len(report_container.innerHTML)}")

def render_grand_totals(grand_totals_by_currency):
    """Render the grand totals section."""
    grand_totals_container = document.getElementById('grand-totals-container')
    if not grand_totals_container:
        print("Error: grand-totals-container not found")
        return

    # Clear previous content but keep the header
    grand_totals_container.innerHTML = '<h3 class="section-subtitle">Grand Totals</h3>'

    if not grand_totals_by_currency:
        grand_totals_container.innerHTML += '<div class="grand-totals-item"><span class="grand-totals-currency">No expenses to total</span><span class="grand-totals-amount">-</span></div>'
    else:
        for currency, total_amount in sorted(grand_totals_by_currency.items()):
            grand_totals_container.innerHTML += f'''
            <div class="grand-totals-item">
                <span class="grand-totals-currency">Total in {currency}</span>
                <span class="grand-totals-amount">{total_amount:.2f}</span>
            </div>
            '''

def setup_event_handlers():
    """Setup event handlers for the page."""
    refresh_btn = document.getElementById("refresh-report-btn")
    if refresh_btn:
        refresh_btn.onclick = lambda _: init()
        print("Refresh button event handler set up")

# --- Entry Point ---
async def main():
    """Main entry point for the report generation."""
    try:
        await generate_report()
        setup_event_handlers()
    except Exception as e:
        print(f"Error in main: {e}")
        show_status(f"Error generating report: {e}", False)

# Auto-execute when script loads
async def init():
    print("Initializing expense report...")
    try:
        await main()
    except Exception as e:
        print(f"Error during initialization: {e}")
        show_status(f"Error during initialization: {e}", False)

# Call the initialization function
print("Starting expense report initialization...")
init()